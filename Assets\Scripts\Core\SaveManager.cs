using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;

namespace AquariumGame.Core
{
    /// <summary>
    /// Handles saving and loading game data with encryption and cloud sync support
    /// </summary>
    public class SaveManager : MonoBehaviour
    {
        [Header("Save Settings")]
        [SerializeField] private bool enableEncryption = true;
        [SerializeField] private bool enableCloudSync = false;
        [SerializeField] private float autoSaveInterval = 300f; // 5 minutes
        [SerializeField] private int maxSaveSlots = 3;
        
        [Header("Debug")]
        [SerializeField] private bool debugMode = false;
        
        // Save file paths
        private string saveDirectory;
        private string currentSaveFile;
        private const string SAVE_FILE_EXTENSION = ".aquarium";
        private const string ENCRYPTION_KEY = "AquariumGame2024"; // In production, this should be more secure
        
        // Auto-save
        private float lastAutoSaveTime;
        
        // Events
        public System.Action OnSaveStarted;
        public System.Action OnSaveCompleted;
        public System.Action OnLoadStarted;
        public System.Action OnLoadCompleted;
        public System.Action<string> OnSaveError;
        public System.Action<string> OnLoadError;
        
        private void Awake()
        {
            InitializeSaveSystem();
        }
        
        private void Update()
        {
            // Auto-save
            if (Time.time - lastAutoSaveTime > autoSaveInterval)
            {
                StartCoroutine(SaveGame());
            }
        }
        
        private void InitializeSaveSystem()
        {
            // Set up save directory
            saveDirectory = Path.Combine(Application.persistentDataPath, "Saves");
            
            if (!Directory.Exists(saveDirectory))
            {
                Directory.CreateDirectory(saveDirectory);
            }
            
            currentSaveFile = Path.Combine(saveDirectory, $"save_slot_1{SAVE_FILE_EXTENSION}");
            
            if (debugMode)
                Debug.Log($"[SaveManager] Save directory: {saveDirectory}");
        }
        
        public IEnumerator SaveGame(int saveSlot = 1)
        {
            OnSaveStarted?.Invoke();
            
            try
            {
                GameSaveData saveData = CollectSaveData();
                string saveFilePath = GetSaveFilePath(saveSlot);
                
                yield return StartCoroutine(WriteSaveFile(saveData, saveFilePath));
                
                lastAutoSaveTime = Time.time;
                OnSaveCompleted?.Invoke();
                
                if (debugMode)
                    Debug.Log($"[SaveManager] Game saved successfully to slot {saveSlot}");
            }
            catch (System.Exception e)
            {
                string error = $"Save failed: {e.Message}";
                OnSaveError?.Invoke(error);
                Debug.LogError($"[SaveManager] {error}");
            }
        }
        
        public IEnumerator LoadGame(int saveSlot = 1)
        {
            OnLoadStarted?.Invoke();
            
            try
            {
                string saveFilePath = GetSaveFilePath(saveSlot);
                
                if (!File.Exists(saveFilePath))
                {
                    if (debugMode)
                        Debug.Log($"[SaveManager] No save file found at slot {saveSlot}, creating new game");
                    
                    CreateNewGameData();
                    OnLoadCompleted?.Invoke();
                    yield break;
                }
                
                GameSaveData saveData = null;
                yield return StartCoroutine(ReadSaveFile(saveFilePath, (data) => saveData = data));
                
                if (saveData != null)
                {
                    ApplySaveData(saveData);
                    OnLoadCompleted?.Invoke();
                    
                    if (debugMode)
                        Debug.Log($"[SaveManager] Game loaded successfully from slot {saveSlot}");
                }
                else
                {
                    throw new System.Exception("Failed to read save data");
                }
            }
            catch (System.Exception e)
            {
                string error = $"Load failed: {e.Message}";
                OnLoadError?.Invoke(error);
                Debug.LogError($"[SaveManager] {error}");
                
                // Fallback to new game
                CreateNewGameData();
                OnLoadCompleted?.Invoke();
            }
        }
        
        private GameSaveData CollectSaveData()
        {
            GameSaveData saveData = new GameSaveData();
            
            // Game metadata
            saveData.version = Application.version;
            saveData.saveTime = System.DateTime.Now.ToBinary();
            saveData.playTime = Time.time; // This should be tracked properly
            
            // Economy data
            if (GameManager.Instance?.EconomyManager != null)
            {
                saveData.economyData = GameManager.Instance.EconomyManager.GetSaveData();
            }
            
            // Fish data
            if (GameManager.Instance?.AquariumManager != null)
            {
                saveData.fishData = CollectFishData();
            }
            
            // Aquarium data
            if (GameManager.Instance?.AquariumManager != null)
            {
                saveData.aquariumData = CollectAquariumData();
            }
            
            // Settings and preferences
            saveData.gameSettings = CollectGameSettings();
            
            return saveData;
        }
        
        private List<FishSaveData> CollectFishData()
        {
            List<FishSaveData> fishDataList = new List<FishSaveData>();
            
            foreach (var fish in GameManager.Instance.AquariumManager.FishInAquarium)
            {
                if (fish != null)
                {
                    FishSaveData fishData = new FishSaveData
                    {
                        uniqueId = fish.UniqueId,
                        fishName = fish.FishName,
                        speciesName = fish.Species?.name ?? "Unknown",
                        size = fish.Size,
                        age = fish.Age,
                        health = fish.Health,
                        happiness = fish.Happiness,
                        hunger = fish.Hunger,
                        position = fish.transform.position,
                        // Genetics would be serialized here
                        geneticsData = SerializeGenetics(fish.Genetics)
                    };
                    
                    fishDataList.Add(fishData);
                }
            }
            
            return fishDataList;
        }
        
        private AquariumSaveData CollectAquariumData()
        {
            var aquarium = GameManager.Instance.AquariumManager;
            
            return new AquariumSaveData
            {
                temperature = aquarium.Temperature,
                pH = aquarium.PH,
                oxygenLevel = aquarium.OxygenLevel,
                cleanlinessLevel = aquarium.CleanlinessLevel,
                lightLevel = aquarium.LightLevel,
                waterType = (int)aquarium.WaterType,
                hasFilter = true, // These would come from aquarium properties
                hasHeater = true,
                hasAerator = true
            };
        }
        
        private GameSettingsSaveData CollectGameSettings()
        {
            return new GameSettingsSaveData
            {
                gameSpeed = GameManager.Instance?.GameSpeed ?? 1.0f,
                debugMode = GameManager.Instance?.IsDebugMode ?? false,
                // Add other settings as needed
            };
        }
        
        private string SerializeGenetics(Fish.FishGenetics genetics)
        {
            if (genetics == null) return "";
            
            // This would serialize the genetics data to JSON or another format
            // For now, return a placeholder
            return JsonUtility.ToJson(genetics);
        }
        
        private IEnumerator WriteSaveFile(GameSaveData saveData, string filePath)
        {
            string jsonData = JsonUtility.ToJson(saveData, true);
            
            if (enableEncryption)
            {
                jsonData = EncryptString(jsonData);
            }
            
            // Write to temporary file first, then move to final location (atomic write)
            string tempFilePath = filePath + ".tmp";
            
            yield return null; // Allow frame to continue
            
            File.WriteAllText(tempFilePath, jsonData);
            
            yield return null;
            
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            
            File.Move(tempFilePath, filePath);
        }
        
        private IEnumerator ReadSaveFile(string filePath, System.Action<GameSaveData> callback)
        {
            string jsonData = File.ReadAllText(filePath);
            
            yield return null;
            
            if (enableEncryption)
            {
                jsonData = DecryptString(jsonData);
            }
            
            yield return null;
            
            GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(jsonData);
            callback?.Invoke(saveData);
        }
        
        private void ApplySaveData(GameSaveData saveData)
        {
            // Apply economy data
            if (saveData.economyData != null && GameManager.Instance?.EconomyManager != null)
            {
                GameManager.Instance.EconomyManager.LoadSaveData(saveData.economyData);
            }
            
            // Apply fish data
            if (saveData.fishData != null)
            {
                ApplyFishData(saveData.fishData);
            }
            
            // Apply aquarium data
            if (saveData.aquariumData != null)
            {
                ApplyAquariumData(saveData.aquariumData);
            }
            
            // Apply game settings
            if (saveData.gameSettings != null)
            {
                ApplyGameSettings(saveData.gameSettings);
            }
        }
        
        private void ApplyFishData(List<FishSaveData> fishDataList)
        {
            // This would instantiate fish from save data
            // Implementation would depend on fish spawning system
            if (debugMode)
                Debug.Log($"[SaveManager] Loading {fishDataList.Count} fish from save data");
        }
        
        private void ApplyAquariumData(AquariumSaveData aquariumData)
        {
            var aquarium = GameManager.Instance?.AquariumManager;
            if (aquarium != null)
            {
                aquarium.SetTemperature(aquariumData.temperature);
                // Apply other aquarium properties
            }
        }
        
        private void ApplyGameSettings(GameSettingsSaveData settingsData)
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.SetGameSpeed(settingsData.gameSpeed);
            }
        }
        
        private void CreateNewGameData()
        {
            // Initialize new game with default values
            if (debugMode)
                Debug.Log("[SaveManager] Creating new game data");
        }
        
        private string GetSaveFilePath(int saveSlot)
        {
            return Path.Combine(saveDirectory, $"save_slot_{saveSlot}{SAVE_FILE_EXTENSION}");
        }
        
        private string EncryptString(string text)
        {
            // Simple XOR encryption (in production, use proper encryption)
            char[] chars = text.ToCharArray();
            for (int i = 0; i < chars.Length; i++)
            {
                chars[i] = (char)(chars[i] ^ ENCRYPTION_KEY[i % ENCRYPTION_KEY.Length]);
            }
            return new string(chars);
        }
        
        private string DecryptString(string encryptedText)
        {
            // XOR decryption (same as encryption for XOR)
            return EncryptString(encryptedText);
        }
        
        public bool SaveSlotExists(int saveSlot)
        {
            return File.Exists(GetSaveFilePath(saveSlot));
        }
        
        public void DeleteSaveSlot(int saveSlot)
        {
            string filePath = GetSaveFilePath(saveSlot);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
                if (debugMode)
                    Debug.Log($"[SaveManager] Deleted save slot {saveSlot}");
            }
        }
        
        // Save data structures
        [System.Serializable]
        public class GameSaveData
        {
            public string version;
            public long saveTime;
            public float playTime;
            public EconomyManager.EconomySaveData economyData;
            public List<FishSaveData> fishData;
            public AquariumSaveData aquariumData;
            public GameSettingsSaveData gameSettings;
        }
        
        [System.Serializable]
        public class FishSaveData
        {
            public string uniqueId;
            public string fishName;
            public string speciesName;
            public float size;
            public float age;
            public float health;
            public float happiness;
            public float hunger;
            public Vector3 position;
            public string geneticsData;
        }
        
        [System.Serializable]
        public class AquariumSaveData
        {
            public float temperature;
            public float pH;
            public float oxygenLevel;
            public float cleanlinessLevel;
            public float lightLevel;
            public int waterType;
            public bool hasFilter;
            public bool hasHeater;
            public bool hasAerator;
        }
        
        [System.Serializable]
        public class GameSettingsSaveData
        {
            public float gameSpeed;
            public bool debugMode;
        }
    }
}
