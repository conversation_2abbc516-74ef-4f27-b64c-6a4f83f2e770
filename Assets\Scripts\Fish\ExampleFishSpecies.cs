using UnityEngine;

namespace AquariumGame.Fish
{
    /// <summary>
    /// Example fish species configurations - these would normally be created as ScriptableObject assets
    /// </summary>
    public static class ExampleFishSpecies
    {
        public static FishSpecies CreateGoldfish()
        {
            var goldfish = ScriptableObject.CreateInstance<FishSpecies>();
            
            goldfish.speciesName = "Goldfish";
            goldfish.description = "A classic aquarium fish known for its golden color and peaceful nature. Perfect for beginners!";
            
            // Physical characteristics
            goldfish.baseSize = 1.0f;
            goldfish.sizeVariation = 0.3f;
            goldfish.possibleColors = new Color[] 
            { 
                new Color(1f, 0.8f, 0f), // Gold
                new Color(1f, 0.6f, 0f), // Orange
                new Color(1f, 1f, 1f)    // White
            };
            goldfish.baseSwimSpeed = 2.0f;
            goldfish.speedVariation = 0.5f;
            
            // Life stats
            goldfish.baseLifespan = 600f; // 10 minutes
            goldfish.lifespanVariation = 120f;
            goldfish.baseHealth = 100f;
            goldfish.healthVariation = 20f;
            
            // Behavior
            goldfish.possiblePersonalities = new Fish.FishPersonality[]
            {
                Fish.FishPersonality.Peaceful,
                Fish.FishPersonality.Curious,
                Fish.FishPersonality.Social
            };
            goldfish.baseSocialLevel = 0.7f;
            goldfish.baseAggressionLevel = 0.1f;
            goldfish.baseCuriosityLevel = 0.8f;
            
            // Breeding
            goldfish.canBreed = true;
            goldfish.breedingCooldown = 180f; // 3 minutes
            goldfish.minBreedingAge = 120f; // 2 minutes
            goldfish.hybridChance = 0.2f;
            
            // Economic
            goldfish.baseSellPrice = 10;
            goldfish.baseBuyPrice = 15;
            goldfish.rarity = FishSpecies.Rarity.Common;
            
            // Environment
            goldfish.preferredTemperature = 22f;
            goldfish.temperatureTolerance = 8f;
            goldfish.preferredPH = 7.0f;
            goldfish.pHTolerance = 1.5f;
            goldfish.preferredWaterType = FishSpecies.WaterType.Freshwater;
            
            // Feeding
            goldfish.preferredFoods = new FishSpecies.FoodType[]
            {
                FishSpecies.FoodType.Flakes,
                FishSpecies.FoodType.Pellets,
                FishSpecies.FoodType.Vegetables
            };
            goldfish.feedingFrequency = 0.05f;
            goldfish.maxHunger = 100f;
            
            return goldfish;
        }
        
        public static FishSpecies CreateBetta()
        {
            var betta = ScriptableObject.CreateInstance<FishSpecies>();
            
            betta.speciesName = "Betta Fish";
            betta.description = "A beautiful and territorial fish with flowing fins. Males are particularly aggressive and colorful.";
            
            // Physical characteristics
            betta.baseSize = 0.8f;
            betta.sizeVariation = 0.2f;
            betta.possibleColors = new Color[]
            {
                new Color(0f, 0f, 1f),   // Blue
                new Color(1f, 0f, 0f),   // Red
                new Color(0.5f, 0f, 1f), // Purple
                new Color(0f, 1f, 0.5f)  // Turquoise
            };
            betta.baseSwimSpeed = 1.5f;
            betta.speedVariation = 0.3f;
            
            // Life stats
            betta.baseLifespan = 480f; // 8 minutes
            betta.lifespanVariation = 60f;
            betta.baseHealth = 80f;
            betta.healthVariation = 15f;
            
            // Behavior
            betta.possiblePersonalities = new Fish.FishPersonality[]
            {
                Fish.FishPersonality.Aggressive,
                Fish.FishPersonality.Solitary,
                Fish.FishPersonality.Energetic
            };
            betta.baseSocialLevel = 0.2f;
            betta.baseAggressionLevel = 0.8f;
            betta.baseCuriosityLevel = 0.6f;
            
            // Breeding
            betta.canBreed = true;
            betta.breedingCooldown = 240f; // 4 minutes
            betta.minBreedingAge = 90f; // 1.5 minutes
            betta.hybridChance = 0.1f;
            
            // Economic
            betta.baseSellPrice = 25;
            betta.baseBuyPrice = 35;
            betta.rarity = FishSpecies.Rarity.Uncommon;
            
            // Special abilities
            betta.specialAbilities = new FishSpecies.SpecialAbility[]
            {
                new FishSpecies.SpecialAbility
                {
                    abilityName = "Territorial Display",
                    description = "Intimidates nearby fish, reducing their aggression",
                    cooldown = 60f,
                    type = FishSpecies.SpecialAbility.AbilityType.Attraction,
                    effectStrength = 0.8f
                }
            };
            
            // Environment
            betta.preferredTemperature = 26f;
            betta.temperatureTolerance = 4f;
            betta.preferredPH = 6.5f;
            betta.pHTolerance = 1.0f;
            betta.preferredWaterType = FishSpecies.WaterType.Freshwater;
            
            // Feeding
            betta.preferredFoods = new FishSpecies.FoodType[]
            {
                FishSpecies.FoodType.LiveFood,
                FishSpecies.FoodType.Meat,
                FishSpecies.FoodType.Pellets
            };
            betta.feedingFrequency = 0.08f;
            betta.maxHunger = 80f;
            
            return betta;
        }
        
        public static FishSpecies CreateAngelfish()
        {
            var angelfish = ScriptableObject.CreateInstance<FishSpecies>();
            
            angelfish.speciesName = "Angelfish";
            angelfish.description = "Elegant fish with distinctive triangular shape and graceful swimming. Popular in community tanks.";
            
            // Physical characteristics
            angelfish.baseSize = 1.5f;
            angelfish.sizeVariation = 0.4f;
            angelfish.possibleColors = new Color[]
            {
                new Color(0.8f, 0.8f, 0.8f), // Silver
                new Color(0f, 0f, 0f),        // Black
                new Color(1f, 1f, 0.8f),      // Marble
                new Color(1f, 0.8f, 0.6f)     // Gold
            };
            angelfish.baseSwimSpeed = 1.8f;
            angelfish.speedVariation = 0.4f;
            
            // Life stats
            angelfish.baseLifespan = 720f; // 12 minutes
            angelfish.lifespanVariation = 180f;
            angelfish.baseHealth = 120f;
            angelfish.healthVariation = 25f;
            
            // Behavior
            angelfish.possiblePersonalities = new Fish.FishPersonality[]
            {
                Fish.FishPersonality.Peaceful,
                Fish.FishPersonality.Social,
                Fish.FishPersonality.Curious
            };
            angelfish.baseSocialLevel = 0.8f;
            angelfish.baseAggressionLevel = 0.3f;
            angelfish.baseCuriosityLevel = 0.7f;
            
            // Breeding
            angelfish.canBreed = true;
            angelfish.breedingCooldown = 300f; // 5 minutes
            angelfish.minBreedingAge = 180f; // 3 minutes
            angelfish.hybridChance = 0.15f;
            
            // Economic
            angelfish.baseSellPrice = 40;
            angelfish.baseBuyPrice = 60;
            angelfish.rarity = FishSpecies.Rarity.Rare;
            
            // Environment
            angelfish.preferredTemperature = 25f;
            angelfish.temperatureTolerance = 5f;
            angelfish.preferredPH = 6.8f;
            angelfish.pHTolerance = 1.2f;
            angelfish.preferredWaterType = FishSpecies.WaterType.Freshwater;
            
            // Feeding
            angelfish.preferredFoods = new FishSpecies.FoodType[]
            {
                FishSpecies.FoodType.Flakes,
                FishSpecies.FoodType.LiveFood,
                FishSpecies.FoodType.Pellets
            };
            angelfish.feedingFrequency = 0.06f;
            angelfish.maxHunger = 120f;
            
            return angelfish;
        }
        
        public static FishSpecies CreateMythicalDragonfish()
        {
            var dragonfish = ScriptableObject.CreateInstance<FishSpecies>();
            
            dragonfish.speciesName = "Mythical Dragonfish";
            dragonfish.description = "A legendary creature of the deep waters. Said to bring good fortune to those who care for it properly. Extremely rare and powerful.";
            
            // Physical characteristics
            dragonfish.baseSize = 2.5f;
            dragonfish.sizeVariation = 0.5f;
            dragonfish.possibleColors = new Color[]
            {
                new Color(1f, 0f, 0f, 0.8f),    // Translucent Red
                new Color(0f, 1f, 0f, 0.8f),    // Translucent Green
                new Color(0.5f, 0f, 1f, 0.8f),  // Translucent Purple
                new Color(1f, 0.8f, 0f, 0.9f)   // Golden
            };
            dragonfish.baseSwimSpeed = 3.5f;
            dragonfish.speedVariation = 0.8f;
            
            // Life stats
            dragonfish.baseLifespan = 1800f; // 30 minutes!
            dragonfish.lifespanVariation = 300f;
            dragonfish.baseHealth = 200f;
            dragonfish.healthVariation = 50f;
            
            // Behavior
            dragonfish.possiblePersonalities = new Fish.FishPersonality[]
            {
                Fish.FishPersonality.Energetic,
                Fish.FishPersonality.Curious,
                Fish.FishPersonality.Social
            };
            dragonfish.baseSocialLevel = 0.9f;
            dragonfish.baseAggressionLevel = 0.1f;
            dragonfish.baseCuriosityLevel = 1.0f;
            
            // Breeding
            dragonfish.canBreed = true;
            dragonfish.breedingCooldown = 600f; // 10 minutes
            dragonfish.minBreedingAge = 300f; // 5 minutes
            dragonfish.hybridChance = 0.5f; // High chance for magical hybrids
            
            // Economic
            dragonfish.baseSellPrice = 1000;
            dragonfish.baseBuyPrice = 2000;
            dragonfish.rarity = FishSpecies.Rarity.Mythical;
            
            // Special abilities
            dragonfish.specialAbilities = new FishSpecies.SpecialAbility[]
            {
                new FishSpecies.SpecialAbility
                {
                    abilityName = "Bioluminescence",
                    description = "Glows with magical light, improving aquarium ambiance",
                    cooldown = 30f,
                    type = FishSpecies.SpecialAbility.AbilityType.Bioluminescence,
                    effectStrength = 1.0f
                },
                new FishSpecies.SpecialAbility
                {
                    abilityName = "Healing Aura",
                    description = "Heals nearby fish and improves their happiness",
                    cooldown = 120f,
                    type = FishSpecies.SpecialAbility.AbilityType.Healing,
                    effectStrength = 0.8f
                },
                new FishSpecies.SpecialAbility
                {
                    abilityName = "Time Dilation",
                    description = "Slows down time in a small area, allowing for better observation",
                    cooldown = 300f,
                    type = FishSpecies.SpecialAbility.AbilityType.TimeSlowing,
                    effectStrength = 0.5f
                }
            };
            
            // Environment (very adaptable)
            dragonfish.preferredTemperature = 24f;
            dragonfish.temperatureTolerance = 15f;
            dragonfish.preferredPH = 7.0f;
            dragonfish.pHTolerance = 3.0f;
            dragonfish.preferredWaterType = FishSpecies.WaterType.Freshwater;
            
            // Feeding (omnivorous)
            dragonfish.preferredFoods = new FishSpecies.FoodType[]
            {
                FishSpecies.FoodType.Flakes,
                FishSpecies.FoodType.Pellets,
                FishSpecies.FoodType.LiveFood,
                FishSpecies.FoodType.Vegetables,
                FishSpecies.FoodType.Meat,
                FishSpecies.FoodType.Plankton,
                FishSpecies.FoodType.Algae
            };
            dragonfish.feedingFrequency = 0.02f; // Eats less frequently
            dragonfish.maxHunger = 200f;
            
            return dragonfish;
        }
        
        /// <summary>
        /// Creates a list of all example species for easy initialization
        /// </summary>
        public static FishSpecies[] GetAllExampleSpecies()
        {
            return new FishSpecies[]
            {
                CreateGoldfish(),
                CreateBetta(),
                CreateAngelfish(),
                CreateMythicalDragonfish()
            };
        }
    }
}
