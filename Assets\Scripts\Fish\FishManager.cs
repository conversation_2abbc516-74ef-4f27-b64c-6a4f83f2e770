using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace AquariumGame.Fish
{
    /// <summary>
    /// Manages fish spawning, breeding, and lifecycle events
    /// </summary>
    public class FishManager : MonoBehaviour
    {
        [Header("Fish Spawning")]
        [SerializeField] private List<FishSpecies> availableSpecies = new List<FishSpecies>();
        [SerializeField] private Transform fishContainer;
        [SerializeField] private float spawnCooldown = 60f; // seconds
        [SerializeField] private int maxWildSpawns = 5;
        
        [Header("Breeding System")]
        [SerializeField] private bool breedingEnabled = true;
        [SerializeField] private float breedingCheckInterval = 30f; // seconds
        [SerializeField] private float breedingSuccessRate = 0.7f;
        [SerializeField] private int maxEggsPerBreeding = 3;
        
        [Header("Fish Lifecycle")]
        [SerializeField] private bool naturalDeathEnabled = true;
        [SerializeField] private float diseaseChance = 0.01f; // 1% chance per check
        [SerializeField] private float diseaseCheckInterval = 60f; // seconds
        
        [Header("Special Events")]
        [SerializeField] private float rareFishEventChance = 0.05f; // 5% chance
        [SerializeField] private float mutationEventChance = 0.02f; // 2% chance
        
        // Internal state
        private float lastSpawnTime;
        private float lastBreedingCheck;
        private float lastDiseaseCheck;
        private List<Fish> managedFish = new List<Fish>();
        private Dictionary<string, int> speciesPopulation = new Dictionary<string, int>();
        
        // Events
        public System.Action<Fish> OnFishSpawned;
        public System.Action<Fish> OnFishDied;
        public System.Action<Fish, Fish, List<Fish>> OnFishBred; // parents + offspring
        public System.Action<Fish> OnRareFishDiscovered;
        public System.Action<Fish> OnFishMutated;
        
        // Properties
        public List<Fish> ManagedFish => managedFish;
        public int TotalFishCount => managedFish.Count;
        public Dictionary<string, int> SpeciesPopulation => speciesPopulation;
        
        public void Initialize()
        {
            if (fishContainer == null)
                fishContainer = new GameObject("Fish Container").transform;
            
            lastSpawnTime = Time.time;
            lastBreedingCheck = Time.time;
            lastDiseaseCheck = Time.time;
            
            UpdateSpeciesPopulation();
            
            Debug.Log($"[FishManager] Initialized with {availableSpecies.Count} available species");
        }
        
        private void Update()
        {
            HandleWildSpawning();
            HandleBreeding();
            HandleDiseaseChecks();
            HandleSpecialEvents();
        }
        
        private void HandleWildSpawning()
        {
            if (Time.time - lastSpawnTime > spawnCooldown)
            {
                if (managedFish.Count < maxWildSpawns)
                {
                    SpawnRandomFish();
                }
                lastSpawnTime = Time.time;
            }
        }
        
        private void HandleBreeding()
        {
            if (!breedingEnabled) return;
            
            if (Time.time - lastBreedingCheck > breedingCheckInterval)
            {
                CheckForBreedingOpportunities();
                lastBreedingCheck = Time.time;
            }
        }
        
        private void HandleDiseaseChecks()
        {
            if (!naturalDeathEnabled) return;
            
            if (Time.time - lastDiseaseCheck > diseaseCheckInterval)
            {
                CheckForDiseases();
                lastDiseaseCheck = Time.time;
            }
        }
        
        private void HandleSpecialEvents()
        {
            // Random special events
            if (Random.Range(0f, 1f) < 0.001f) // Very rare chance per frame
            {
                TriggerSpecialEvent();
            }
        }
        
        public Fish SpawnFish(FishSpecies species, Vector3 position, FishGenetics genetics = null)
        {
            if (species == null || species.prefab == null)
            {
                Debug.LogError("[FishManager] Cannot spawn fish: invalid species or prefab");
                return null;
            }
            
            // Check aquarium capacity
            if (GameManager.Instance?.AquariumManager != null)
            {
                if (GameManager.Instance.AquariumManager.FishCount >= GameManager.Instance.AquariumManager.MaxCapacity)
                {
                    Debug.LogWarning("[FishManager] Cannot spawn fish: aquarium at capacity");
                    return null;
                }
            }
            
            // Instantiate fish
            GameObject fishObject = Instantiate(species.prefab, position, Quaternion.identity, fishContainer);
            Fish fish = fishObject.GetComponent<Fish>();
            
            if (fish == null)
            {
                Debug.LogError("[FishManager] Fish prefab missing Fish component");
                Destroy(fishObject);
                return null;
            }
            
            // Initialize fish properties
            InitializeFish(fish, species, genetics);
            
            // Add to management lists
            managedFish.Add(fish);
            UpdateSpeciesPopulation();
            
            // Subscribe to fish events
            fish.OnFishDied += HandleFishDeath;
            
            // Add to aquarium
            if (GameManager.Instance?.AquariumManager != null)
            {
                GameManager.Instance.AquariumManager.AddFish(fish);
            }
            
            OnFishSpawned?.Invoke(fish);
            
            Debug.Log($"[FishManager] Spawned {species.speciesName}: {fish.FishName}");
            return fish;
        }
        
        public Fish SpawnRandomFish()
        {
            if (availableSpecies.Count == 0)
            {
                Debug.LogWarning("[FishManager] No available species to spawn");
                return null;
            }
            
            // Select species based on rarity
            FishSpecies selectedSpecies = SelectSpeciesByRarity();
            
            // Get random position in aquarium
            Vector3 spawnPosition = Vector3.zero;
            if (GameManager.Instance?.AquariumManager != null)
            {
                spawnPosition = GameManager.Instance.AquariumManager.GetRandomPositionInAquarium();
            }
            
            return SpawnFish(selectedSpecies, spawnPosition);
        }
        
        private FishSpecies SelectSpeciesByRarity()
        {
            // Weighted selection based on rarity
            float totalWeight = 0f;
            Dictionary<FishSpecies, float> weights = new Dictionary<FishSpecies, float>();
            
            foreach (var species in availableSpecies)
            {
                float weight = GetSpeciesWeight(species.rarity);
                weights[species] = weight;
                totalWeight += weight;
            }
            
            float randomValue = Random.Range(0f, totalWeight);
            float currentWeight = 0f;
            
            foreach (var kvp in weights)
            {
                currentWeight += kvp.Value;
                if (randomValue <= currentWeight)
                {
                    return kvp.Key;
                }
            }
            
            return availableSpecies[0]; // Fallback
        }
        
        private float GetSpeciesWeight(FishSpecies.Rarity rarity)
        {
            switch (rarity)
            {
                case FishSpecies.Rarity.Common: return 100f;
                case FishSpecies.Rarity.Uncommon: return 50f;
                case FishSpecies.Rarity.Rare: return 20f;
                case FishSpecies.Rarity.Epic: return 5f;
                case FishSpecies.Rarity.Legendary: return 1f;
                case FishSpecies.Rarity.Mythical: return 0.1f;
                default: return 100f;
            }
        }
        
        private void InitializeFish(Fish fish, FishSpecies species, FishGenetics genetics = null)
        {
            // Set species
            // fish.SetSpecies(species); // This method would need to be added to Fish class
            
            // Generate or apply genetics
            if (genetics == null)
            {
                genetics = new FishGenetics();
            }
            // fish.SetGenetics(genetics); // This method would need to be added to Fish class
            
            // Apply species-specific properties
            // This would set size, colors, personality, etc. based on species and genetics
        }
        
        private void CheckForBreedingOpportunities()
        {
            // Find compatible fish pairs
            for (int i = 0; i < managedFish.Count; i++)
            {
                for (int j = i + 1; j < managedFish.Count; j++)
                {
                    Fish fish1 = managedFish[i];
                    Fish fish2 = managedFish[j];
                    
                    if (CanBreed(fish1, fish2))
                    {
                        AttemptBreeding(fish1, fish2);
                    }
                }
            }
        }
        
        private bool CanBreed(Fish fish1, Fish fish2)
        {
            if (fish1 == null || fish2 == null) return false;
            if (fish1.Species == null || fish2.Species == null) return false;
            
            // Check species compatibility
            if (!fish1.Species.IsCompatibleWith(fish2.Species)) return false;
            
            // Check age requirements
            if (fish1.Age < fish1.Species.minBreedingAge || fish2.Age < fish2.Species.minBreedingAge)
                return false;
            
            // Check health requirements
            if (fish1.Health < 70f || fish2.Health < 70f) return false;
            
            // Check happiness requirements
            if (fish1.Happiness < 70f || fish2.Happiness < 70f) return false;
            
            // Check proximity (fish should be near each other)
            float distance = Vector3.Distance(fish1.transform.position, fish2.transform.position);
            if (distance > 3f) return false;
            
            return true;
        }
        
        private void AttemptBreeding(Fish parent1, Fish parent2)
        {
            if (Random.Range(0f, 1f) > breedingSuccessRate) return;
            
            int eggCount = Random.Range(1, maxEggsPerBreeding + 1);
            List<Fish> offspring = new List<Fish>();
            
            for (int i = 0; i < eggCount; i++)
            {
                // Create offspring genetics from parents
                FishGenetics offspringGenetics = new FishGenetics(parent1.Genetics, parent2.Genetics);
                
                // Determine offspring species (could be hybrid)
                FishSpecies offspringSpecies = DetermineOffspringSpecies(parent1.Species, parent2.Species);
                
                // Spawn offspring near parents
                Vector3 spawnPos = Vector3.Lerp(parent1.transform.position, parent2.transform.position, 0.5f);
                spawnPos += Random.insideUnitSphere * 2f; // Add some randomness
                
                Fish offspring_fish = SpawnFish(offspringSpecies, spawnPos, offspringGenetics);
                if (offspring_fish != null)
                {
                    offspring.Add(offspring_fish);
                }
            }
            
            if (offspring.Count > 0)
            {
                OnFishBred?.Invoke(parent1, parent2, offspring);
                
                // Give parents breeding cooldown
                // This would be implemented in the Fish class
                
                Debug.Log($"[FishManager] Breeding successful: {parent1.FishName} + {parent2.FishName} = {offspring.Count} offspring");
            }
        }
        
        private FishSpecies DetermineOffspringSpecies(FishSpecies parent1Species, FishSpecies parent2Species)
        {
            // Same species breeding
            if (parent1Species == parent2Species)
                return parent1Species;
            
            // Hybrid breeding
            if (Random.Range(0f, 1f) < parent1Species.hybridChance)
            {
                // Create hybrid (for now, just pick one parent's species)
                return Random.Range(0f, 1f) > 0.5f ? parent1Species : parent2Species;
            }
            
            return parent1Species; // Default to first parent
        }
        
        private void CheckForDiseases()
        {
            foreach (var fish in managedFish.ToList()) // ToList to avoid modification during iteration
            {
                if (fish == null) continue;
                
                if (Random.Range(0f, 1f) < diseaseChance)
                {
                    // Apply disease effects
                    // fish.ApplyDisease(); // This would be implemented in Fish class
                    
                    Debug.Log($"[FishManager] {fish.FishName} contracted a disease");
                }
            }
        }
        
        private void TriggerSpecialEvent()
        {
            float eventRoll = Random.Range(0f, 1f);
            
            if (eventRoll < rareFishEventChance)
            {
                SpawnRareFish();
            }
            else if (eventRoll < rareFishEventChance + mutationEventChance)
            {
                TriggerMutationEvent();
            }
        }
        
        private void SpawnRareFish()
        {
            // Spawn a rare or legendary fish
            var rareSpecies = availableSpecies.Where(s => 
                s.rarity == FishSpecies.Rarity.Rare || 
                s.rarity == FishSpecies.Rarity.Epic ||
                s.rarity == FishSpecies.Rarity.Legendary).ToList();
            
            if (rareSpecies.Count > 0)
            {
                FishSpecies selectedSpecies = rareSpecies[Random.Range(0, rareSpecies.Count)];
                Vector3 spawnPos = GameManager.Instance?.AquariumManager?.GetRandomPositionInAquarium() ?? Vector3.zero;
                
                Fish rareFish = SpawnFish(selectedSpecies, spawnPos);
                if (rareFish != null)
                {
                    OnRareFishDiscovered?.Invoke(rareFish);
                    Debug.Log($"[FishManager] Rare fish event: {rareFish.FishName} appeared!");
                }
            }
        }
        
        private void TriggerMutationEvent()
        {
            if (managedFish.Count == 0) return;
            
            Fish randomFish = managedFish[Random.Range(0, managedFish.Count)];
            if (randomFish != null)
            {
                // Apply random mutation to fish genetics
                // randomFish.Genetics.ApplyRandomMutation(); // This would be implemented
                
                OnFishMutated?.Invoke(randomFish);
                Debug.Log($"[FishManager] Mutation event: {randomFish.FishName} mutated!");
            }
        }
        
        private void HandleFishDeath(Fish deadFish)
        {
            managedFish.Remove(deadFish);
            UpdateSpeciesPopulation();
            
            OnFishDied?.Invoke(deadFish);
            
            Debug.Log($"[FishManager] Fish died: {deadFish.FishName}");
        }
        
        private void UpdateSpeciesPopulation()
        {
            speciesPopulation.Clear();
            
            foreach (var fish in managedFish)
            {
                if (fish?.Species != null)
                {
                    string speciesName = fish.Species.name;
                    if (speciesPopulation.ContainsKey(speciesName))
                        speciesPopulation[speciesName]++;
                    else
                        speciesPopulation[speciesName] = 1;
                }
            }
        }
        
        public List<Fish> GetFishBySpecies(FishSpecies species)
        {
            return managedFish.Where(f => f.Species == species).ToList();
        }
        
        public Fish GetFishById(string uniqueId)
        {
            return managedFish.FirstOrDefault(f => f.UniqueId == uniqueId);
        }
        
        public void RemoveFish(Fish fish)
        {
            if (managedFish.Remove(fish))
            {
                UpdateSpeciesPopulation();
                
                // Unsubscribe from events
                fish.OnFishDied -= HandleFishDeath;
            }
        }
        
        public string GetPopulationReport()
        {
            string report = "<b>Fish Population Report</b>\n";
            report += $"Total Fish: {managedFish.Count}\n\n";
            
            foreach (var kvp in speciesPopulation.OrderByDescending(x => x.Value))
            {
                report += $"{kvp.Key}: {kvp.Value}\n";
            }
            
            return report;
        }
    }
}
