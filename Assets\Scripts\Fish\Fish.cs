using UnityEngine;
using System.Collections.Generic;

namespace AquariumGame.Fish
{
    /// <summary>
    /// Individual fish behavior and properties - enhanced beyond Tiny Aquarium
    /// </summary>
    public class Fish : MonoBehaviour
    {
        [Header("Fish Identity")]
        [SerializeField] private string fishName;
        [SerializeField] private FishSpecies species;
        [SerializeField] private string uniqueId;
        
        [Header("Physical Properties")]
        [SerializeField] private float size = 1.0f;
        [SerializeField] private Color primaryColor = Color.white;
        [SerializeField] private Color secondaryColor = Color.gray;
        [SerializeField] private float swimSpeed = 2.0f;
        
        [Header("Life Stats")]
        [SerializeField] private float health = 100f;
        [SerializeField] private float happiness = 100f;
        [SerializeField] private float hunger = 0f;
        [SerializeField] private float age = 0f;
        [SerializeField] private float lifespan = 300f; // seconds
        
        [Header("Behavior")]
        [SerializeField] private FishPersonality personality;
        [SerializeField] private float socialLevel = 0.5f;
        [SerializeField] private float aggressionLevel = 0.2f;
        [SerializeField] private float curiosityLevel = 0.7f;
        
        [Header("Genetics - NEW FEATURE")]
        [SerializeField] private FishGenetics genetics;
        
        [Header("Movement")]
        [SerializeField] private Transform[] waypoints;
        [SerializeField] private float waypointReachDistance = 0.5f;
        
        // Components
        private Rigidbody fishRigidbody;
        private Animator fishAnimator;
        private AudioSource audioSource;
        
        // State
        private FishState currentState = FishState.Swimming;
        private Vector3 targetPosition;
        private int currentWaypointIndex = 0;
        private float lastFeedTime;
        private float lastInteractionTime;
        
        // Events
        public System.Action<Fish> OnFishDied;
        public System.Action<Fish> OnFishLevelUp;
        public System.Action<Fish, Fish> OnFishInteraction;
        
        // Properties
        public string FishName => fishName;
        public FishSpecies Species => species;
        public string UniqueId => uniqueId;
        public float Size => size;
        public float Health => health;
        public float Happiness => happiness;
        public float Hunger => hunger;
        public float Age => age;
        public FishGenetics Genetics => genetics;
        public FishState CurrentState => currentState;
        
        public enum FishState
        {
            Swimming,
            Feeding,
            Resting,
            Socializing,
            Exploring,
            Breeding,
            Dying
        }
        
        public enum FishPersonality
        {
            Peaceful,
            Aggressive,
            Shy,
            Curious,
            Lazy,
            Energetic,
            Social,
            Solitary
        }
        
        private void Awake()
        {
            // Generate unique ID if not set
            if (string.IsNullOrEmpty(uniqueId))
                uniqueId = System.Guid.NewGuid().ToString();
                
            // Get components
            fishRigidbody = GetComponent<Rigidbody>();
            fishAnimator = GetComponent<Animator>();
            audioSource = GetComponent<AudioSource>();
            
            // Initialize genetics if not set
            if (genetics == null)
                genetics = new FishGenetics();
        }
        
        private void Start()
        {
            InitializeFish();
            SetRandomTarget();
        }
        
        private void Update()
        {
            UpdateLifeStats();
            UpdateBehavior();
            UpdateMovement();
            UpdateAnimations();
        }
        
        private void InitializeFish()
        {
            // Apply genetic traits to physical properties
            ApplyGeneticTraits();
            
            // Set random name if not provided
            if (string.IsNullOrEmpty(fishName))
                fishName = GenerateRandomName();
                
            // Initialize stats based on species and genetics
            InitializeStatsFromSpecies();
            
            lastFeedTime = Time.time;
            lastInteractionTime = Time.time;
        }
        
        private void ApplyGeneticTraits()
        {
            if (genetics != null)
            {
                // Apply size from genetics
                size = genetics.GetTraitValue("size", size);
                transform.localScale = Vector3.one * size;
                
                // Apply colors from genetics
                primaryColor = genetics.GetColorTrait("primaryColor", primaryColor);
                secondaryColor = genetics.GetColorTrait("secondaryColor", secondaryColor);
                
                // Apply behavioral traits
                swimSpeed = genetics.GetTraitValue("speed", swimSpeed);
                socialLevel = genetics.GetTraitValue("social", socialLevel);
                aggressionLevel = genetics.GetTraitValue("aggression", aggressionLevel);
                curiosityLevel = genetics.GetTraitValue("curiosity", curiosityLevel);
                
                // Apply colors to renderer
                ApplyColorsToRenderer();
            }
        }
        
        private void ApplyColorsToRenderer()
        {
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null && renderer.material != null)
            {
                renderer.material.color = primaryColor;
                // Apply secondary color to specific material properties if available
                if (renderer.material.HasProperty("_SecondaryColor"))
                    renderer.material.SetColor("_SecondaryColor", secondaryColor);
            }
        }
        
        private void UpdateLifeStats()
        {
            float deltaTime = Time.deltaTime;
            
            // Age the fish
            age += deltaTime;
            
            // Increase hunger over time
            hunger += deltaTime * 0.1f;
            hunger = Mathf.Clamp01(hunger);
            
            // Decrease happiness based on hunger and other factors
            if (hunger > 0.7f)
                happiness -= deltaTime * 5f;
            else if (hunger < 0.3f)
                happiness += deltaTime * 2f;
                
            happiness = Mathf.Clamp(happiness, 0f, 100f);
            
            // Health is affected by happiness and age
            if (happiness < 30f)
                health -= deltaTime * 2f;
            else if (happiness > 70f)
                health += deltaTime * 0.5f;
                
            // Age affects health
            float ageRatio = age / lifespan;
            if (ageRatio > 0.8f)
                health -= deltaTime * (ageRatio * 3f);
                
            health = Mathf.Clamp(health, 0f, 100f);
            
            // Check for death
            if (health <= 0f || age >= lifespan)
            {
                Die();
            }
        }
        
        private void UpdateBehavior()
        {
            // State machine for fish behavior
            switch (currentState)
            {
                case FishState.Swimming:
                    HandleSwimmingBehavior();
                    break;
                case FishState.Feeding:
                    HandleFeedingBehavior();
                    break;
                case FishState.Resting:
                    HandleRestingBehavior();
                    break;
                case FishState.Socializing:
                    HandleSocializingBehavior();
                    break;
                case FishState.Exploring:
                    HandleExploringBehavior();
                    break;
            }
            
            // Random behavior changes based on personality
            if (Random.Range(0f, 1f) < 0.01f) // 1% chance per frame
            {
                ChangeStateBasedOnPersonality();
            }
        }
        
        private void HandleSwimmingBehavior()
        {
            // Normal swimming behavior - move towards target
            if (Vector3.Distance(transform.position, targetPosition) < waypointReachDistance)
            {
                SetRandomTarget();
            }
        }
        
        private void ChangeStateBasedOnPersonality()
        {
            switch (personality)
            {
                case FishPersonality.Curious:
                    if (Random.Range(0f, 1f) < curiosityLevel)
                        currentState = FishState.Exploring;
                    break;
                case FishPersonality.Social:
                    if (Random.Range(0f, 1f) < socialLevel)
                        currentState = FishState.Socializing;
                    break;
                case FishPersonality.Lazy:
                    if (Random.Range(0f, 1f) < 0.3f)
                        currentState = FishState.Resting;
                    break;
            }
        }
        
        // Additional behavior methods would be implemented here...
        private void HandleFeedingBehavior() { /* Implementation */ }
        private void HandleRestingBehavior() { /* Implementation */ }
        private void HandleSocializingBehavior() { /* Implementation */ }
        private void HandleExploringBehavior() { /* Implementation */ }
        
        private void UpdateMovement()
        {
            if (currentState == FishState.Resting) return;
            
            // Move towards target position
            Vector3 direction = (targetPosition - transform.position).normalized;
            fishRigidbody.velocity = direction * swimSpeed;
            
            // Rotate to face movement direction
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
        }
        
        private void UpdateAnimations()
        {
            if (fishAnimator != null)
            {
                fishAnimator.SetFloat("SwimSpeed", fishRigidbody.velocity.magnitude);
                fishAnimator.SetInt("State", (int)currentState);
                fishAnimator.SetFloat("Happiness", happiness / 100f);
            }
        }
        
        private void SetRandomTarget()
        {
            // Set a random position within the aquarium bounds
            // This would be improved to use actual aquarium boundaries
            targetPosition = new Vector3(
                Random.Range(-5f, 5f),
                Random.Range(-2f, 2f),
                Random.Range(-5f, 5f)
            );
        }
        
        public void Feed(float nutritionValue = 20f)
        {
            hunger = Mathf.Max(0f, hunger - nutritionValue / 100f);
            happiness += 10f;
            lastFeedTime = Time.time;
            currentState = FishState.Feeding;
            
            // Play feeding animation and sound
            if (audioSource != null)
                audioSource.Play();
        }
        
        public void InteractWith(Fish otherFish)
        {
            OnFishInteraction?.Invoke(this, otherFish);
            lastInteractionTime = Time.time;
            
            // Social fish get happiness boost from interactions
            if (personality == FishPersonality.Social)
                happiness += 5f;
        }
        
        private void Die()
        {
            currentState = FishState.Dying;
            OnFishDied?.Invoke(this);
            
            // Play death animation and remove after delay
            StartCoroutine(DeathSequence());
        }
        
        private System.Collections.IEnumerator DeathSequence()
        {
            // Play death animation
            if (fishAnimator != null)
                fishAnimator.SetTrigger("Die");
                
            yield return new WaitForSeconds(3f);
            
            // Remove fish from aquarium
            Destroy(gameObject);
        }
        
        private string GenerateRandomName()
        {
            string[] names = { "Bubbles", "Finn", "Splash", "Coral", "Neptune", "Marina", "Aqua", "Pearl" };
            return names[Random.Range(0, names.Length)];
        }
        
        private void InitializeStatsFromSpecies()
        {
            if (species != null)
            {
                lifespan = species.baseLifespan;
                swimSpeed = species.baseSwimSpeed;
                // Apply other species-specific stats
            }
        }
    }
}
