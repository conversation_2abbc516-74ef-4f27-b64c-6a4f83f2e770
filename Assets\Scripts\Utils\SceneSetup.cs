using UnityEngine;

namespace AquariumGame.Utils
{
    /// <summary>
    /// Utility script to set up the aquarium scene with all necessary components
    /// This helps get the game running quickly for testing
    /// </summary>
    public class SceneSetup : MonoBehaviour
    {
        [Header("Auto Setup")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool createExampleFish = true;
        [SerializeField] private int initialFishCount = 3;
        
        [<PERSON><PERSON>("Prefabs (Assign in Inspector)")]
        [SerializeField] private GameObject fishPrefab;
        [SerializeField] private GameObject aquariumPrefab;
        [SerializeField] private GameObject uiCanvasPrefab;
        
        [Header("Scene Objects")]
        [SerializeField] private Transform aquariumContainer;
        [SerializeField] private Transform fishContainer;
        [SerializeField] private Transform uiContainer;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupScene();
            }
        }
        
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            Debug.Log("[SceneSetup] Setting up aquarium scene...");
            
            CreateContainers();
            SetupGameManager();
            SetupAquarium();
            SetupUI();
            
            if (createExampleFish)
            {
                CreateExampleFish();
            }
            
            Debug.Log("[SceneSetup] Scene setup complete!");
        }
        
        private void CreateContainers()
        {
            // Create main containers if they don't exist
            if (aquariumContainer == null)
            {
                GameObject container = new GameObject("Aquarium Container");
                aquariumContainer = container.transform;
            }
            
            if (fishContainer == null)
            {
                GameObject container = new GameObject("Fish Container");
                fishContainer = container.transform;
            }
            
            if (uiContainer == null)
            {
                GameObject container = new GameObject("UI Container");
                uiContainer = container.transform;
            }
        }
        
        private void SetupGameManager()
        {
            // Check if GameManager already exists
            if (Core.GameManager.Instance != null)
            {
                Debug.Log("[SceneSetup] GameManager already exists");
                return;
            }
            
            // Create GameManager
            GameObject gameManagerObj = new GameObject("GameManager");
            var gameManager = gameManagerObj.AddComponent<Core.GameManager>();
            
            // Create and attach sub-managers
            GameObject managersContainer = new GameObject("Managers");
            managersContainer.transform.SetParent(gameManagerObj.transform);
            
            // Economy Manager
            GameObject economyManagerObj = new GameObject("EconomyManager");
            economyManagerObj.transform.SetParent(managersContainer.transform);
            economyManagerObj.AddComponent<Core.EconomyManager>();
            
            // Aquarium Manager
            GameObject aquariumManagerObj = new GameObject("AquariumManager");
            aquariumManagerObj.transform.SetParent(managersContainer.transform);
            var aquariumManager = aquariumManagerObj.AddComponent<Aquarium.AquariumManager>();
            
            // Fish Manager
            GameObject fishManagerObj = new GameObject("FishManager");
            fishManagerObj.transform.SetParent(managersContainer.transform);
            var fishManager = fishManagerObj.AddComponent<Fish.FishManager>();
            
            // Save Manager
            GameObject saveManagerObj = new GameObject("SaveManager");
            saveManagerObj.transform.SetParent(managersContainer.transform);
            saveManagerObj.AddComponent<Core.SaveManager>();
            
            Debug.Log("[SceneSetup] GameManager and sub-managers created");
        }
        
        private void SetupAquarium()
        {
            // Create basic aquarium environment
            if (aquariumPrefab != null)
            {
                Instantiate(aquariumPrefab, aquariumContainer);
            }
            else
            {
                CreateBasicAquarium();
            }
        }
        
        private void CreateBasicAquarium()
        {
            // Create a simple aquarium using primitives
            GameObject aquarium = new GameObject("Basic Aquarium");
            aquarium.transform.SetParent(aquariumContainer);
            
            // Aquarium walls (invisible but with colliders for fish boundaries)
            CreateAquariumWall("Bottom Wall", new Vector3(0, -3, 0), new Vector3(10, 0.1f, 10));
            CreateAquariumWall("Top Wall", new Vector3(0, 3, 0), new Vector3(10, 0.1f, 10));
            CreateAquariumWall("Left Wall", new Vector3(-5, 0, 0), new Vector3(0.1f, 6, 10));
            CreateAquariumWall("Right Wall", new Vector3(5, 0, 0), new Vector3(0.1f, 6, 10));
            CreateAquariumWall("Front Wall", new Vector3(0, 0, -5), new Vector3(10, 6, 0.1f));
            CreateAquariumWall("Back Wall", new Vector3(0, 0, 5), new Vector3(10, 6, 0.1f));
            
            // Add some basic decorations
            CreateDecoration("Rock 1", new Vector3(-2, -2.5f, 2), new Vector3(1, 1, 1));
            CreateDecoration("Rock 2", new Vector3(3, -2.5f, -1), new Vector3(0.8f, 1.2f, 0.8f));
            CreateDecoration("Plant 1", new Vector3(-3, -2.5f, -3), new Vector3(0.5f, 2, 0.5f));
            CreateDecoration("Plant 2", new Vector3(2, -2.5f, 3), new Vector3(0.4f, 1.8f, 0.4f));
            
            Debug.Log("[SceneSetup] Basic aquarium created");
        }
        
        private void CreateAquariumWall(string name, Vector3 position, Vector3 scale)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = name;
            wall.transform.position = position;
            wall.transform.localScale = scale;
            wall.transform.SetParent(aquariumContainer);
            
            // Make walls invisible but keep colliders
            var renderer = wall.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.enabled = false;
            }
            
            // Set up collider for fish boundaries
            var collider = wall.GetComponent<Collider>();
            if (collider != null)
            {
                collider.isTrigger = false; // Fish should bounce off walls
            }
        }
        
        private void CreateDecoration(string name, Vector3 position, Vector3 scale)
        {
            GameObject decoration = GameObject.CreatePrimitive(PrimitiveType.Cube);
            decoration.name = name;
            decoration.transform.position = position;
            decoration.transform.localScale = scale;
            decoration.transform.SetParent(aquariumContainer);
            
            // Give decorations a different color
            var renderer = decoration.GetComponent<Renderer>();
            if (renderer != null)
            {
                if (name.Contains("Rock"))
                {
                    renderer.material.color = new Color(0.5f, 0.5f, 0.5f); // Gray for rocks
                }
                else if (name.Contains("Plant"))
                {
                    renderer.material.color = new Color(0.2f, 0.8f, 0.2f); // Green for plants
                }
            }
        }
        
        private void SetupUI()
        {
            // Create basic UI canvas
            if (uiCanvasPrefab != null)
            {
                Instantiate(uiCanvasPrefab, uiContainer);
            }
            else
            {
                CreateBasicUI();
            }
        }
        
        private void CreateBasicUI()
        {
            // Create Canvas
            GameObject canvasObj = new GameObject("UI Canvas");
            canvasObj.transform.SetParent(uiContainer);
            
            var canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            
            canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            // Add AquariumUI component
            canvasObj.AddComponent<UI.AquariumUI>();
            
            Debug.Log("[SceneSetup] Basic UI created");
        }
        
        private void CreateExampleFish()
        {
            var fishManager = FindObjectOfType<Fish.FishManager>();
            if (fishManager == null)
            {
                Debug.LogWarning("[SceneSetup] FishManager not found, cannot create example fish");
                return;
            }
            
            // Create example fish species
            var exampleSpecies = Fish.ExampleFishSpecies.GetAllExampleSpecies();
            
            for (int i = 0; i < initialFishCount && i < exampleSpecies.Length; i++)
            {
                Vector3 randomPosition = new Vector3(
                    Random.Range(-3f, 3f),
                    Random.Range(-1f, 1f),
                    Random.Range(-3f, 3f)
                );
                
                // For now, we'll create simple fish GameObjects since we don't have prefabs yet
                CreateSimpleFish(exampleSpecies[i], randomPosition);
            }
            
            Debug.Log($"[SceneSetup] Created {initialFishCount} example fish");
        }
        
        private void CreateSimpleFish(Fish.FishSpecies species, Vector3 position)
        {
            // Create a simple fish using a primitive
            GameObject fishObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            fishObj.name = $"Fish - {species.speciesName}";
            fishObj.transform.position = position;
            fishObj.transform.localScale = Vector3.one * species.baseSize;
            fishObj.transform.SetParent(fishContainer);
            
            // Add Rigidbody for movement
            var rigidbody = fishObj.AddComponent<Rigidbody>();
            rigidbody.useGravity = false; // Fish float in water
            rigidbody.drag = 2f; // Water resistance
            
            // Add Fish component
            var fish = fishObj.AddComponent<Fish.Fish>();
            
            // Set fish color based on species
            var renderer = fishObj.GetComponent<Renderer>();
            if (renderer != null && species.possibleColors.Length > 0)
            {
                renderer.material.color = species.possibleColors[0];
            }
            
            // Add to aquarium manager
            var aquariumManager = FindObjectOfType<Aquarium.AquariumManager>();
            if (aquariumManager != null)
            {
                aquariumManager.AddFish(fish);
            }
        }
        
        [ContextMenu("Clear Scene")]
        public void ClearScene()
        {
            // Clean up created objects
            if (aquariumContainer != null)
                DestroyImmediate(aquariumContainer.gameObject);
            if (fishContainer != null)
                DestroyImmediate(fishContainer.gameObject);
            if (uiContainer != null)
                DestroyImmediate(uiContainer.gameObject);
                
            var gameManager = FindObjectOfType<Core.GameManager>();
            if (gameManager != null)
                DestroyImmediate(gameManager.gameObject);
                
            Debug.Log("[SceneSetup] Scene cleared");
        }
        
        private void OnValidate()
        {
            // Ensure initial fish count is reasonable
            initialFishCount = Mathf.Clamp(initialFishCount, 0, 10);
        }
    }
}
