using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;

namespace AquariumGame.UI
{
    /// <summary>
    /// Main UI controller for the aquarium view - shows our improvements over Tiny Aquarium
    /// </summary>
    public class AquariumUI : MonoBehaviour
    {
        [Header("Status Panels")]
        [SerializeField] private GameObject statusPanel;
        [SerializeField] private TextMeshProUGUI aquariumStatusText;
        [SerializeField] private TextMeshProUGUI economyStatusText;
        [SerializeField] private TextMeshProUGUI fishPopulationText;
        
        [Header("Fish Information")]
        [SerializeField] private GameObject fishInfoPanel;
        [SerializeField] private TextMeshProUG<PERSON> selectedFishName;
        [SerializeField] private TextMeshP<PERSON>UG<PERSON> selectedFishStats;
        [SerializeField] private TextMeshProUGUI selectedFishGenetics;
        [SerializeField] private Image selectedFishImage;
        
        [Header("Controls")]
        [SerializeField] private Button feedAllButton;
        [SerializeField] private Button cleanAquariumButton;
        [SerializeField] private Button spawnFishButton;
        [SerializeField] private Button saveGameButton;
        [SerializeField] private Slider gameSpeedSlider;
        [SerializeField] private TextMeshProUGUI gameSpeedText;
        
        [Header("Advanced Features - NEW!")]
        [SerializeField] private GameObject breedingPanel;
        [SerializeField] private Button enableBreedingButton;
        [SerializeField] private TextMeshProUGUI breedingStatusText;
        [SerializeField] private GameObject geneticsPanel;
        [SerializeField] private Button showGeneticsButton;
        [SerializeField] private GameObject photographyPanel;
        [SerializeField] private Button photographyModeButton;
        
        [Header("Market Panel - ENHANCED")]
        [SerializeField] private GameObject marketPanel;
        [SerializeField] private Button openMarketButton;
        [SerializeField] private Transform marketItemContainer;
        [SerializeField] private GameObject marketItemPrefab;
        
        [Header("Social Features - NEW!")]
        [SerializeField] private GameObject socialPanel;
        [SerializeField] private Button socialHubButton;
        [SerializeField] private TextMeshProUGUI friendsOnlineText;
        [SerializeField] private Button visitFriendButton;
        
        // References
        private Core.GameManager gameManager;
        private Aquarium.AquariumManager aquariumManager;
        private Core.EconomyManager economyManager;
        private Fish.FishManager fishManager;
        private Fish.Fish selectedFish;
        
        // UI State
        private bool isStatusPanelVisible = true;
        private bool isBreedingEnabled = false;
        private bool isPhotographyMode = false;
        
        private void Start()
        {
            InitializeUI();
            SetupEventListeners();
        }
        
        private void InitializeUI()
        {
            // Get manager references
            gameManager = Core.GameManager.Instance;
            if (gameManager != null)
            {
                aquariumManager = gameManager.AquariumManager;
                economyManager = gameManager.EconomyManager;
                fishManager = gameManager.FishManager;
            }
            
            // Initialize UI elements
            if (fishInfoPanel != null)
                fishInfoPanel.SetActive(false);
            
            if (breedingPanel != null)
                breedingPanel.SetActive(false);
                
            if (geneticsPanel != null)
                geneticsPanel.SetActive(false);
                
            if (photographyPanel != null)
                photographyPanel.SetActive(false);
                
            if (marketPanel != null)
                marketPanel.SetActive(false);
                
            if (socialPanel != null)
                socialPanel.SetActive(false);
            
            // Set initial game speed
            if (gameSpeedSlider != null)
            {
                gameSpeedSlider.value = gameManager?.GameSpeed ?? 1.0f;
                UpdateGameSpeedText();
            }
        }
        
        private void SetupEventListeners()
        {
            // Basic controls
            if (feedAllButton != null)
                feedAllButton.onClick.AddListener(FeedAllFish);
                
            if (cleanAquariumButton != null)
                cleanAquariumButton.onClick.AddListener(CleanAquarium);
                
            if (spawnFishButton != null)
                spawnFishButton.onClick.AddListener(SpawnRandomFish);
                
            if (saveGameButton != null)
                saveGameButton.onClick.AddListener(SaveGame);
                
            if (gameSpeedSlider != null)
                gameSpeedSlider.onValueChanged.AddListener(OnGameSpeedChanged);
            
            // Advanced features
            if (enableBreedingButton != null)
                enableBreedingButton.onClick.AddListener(ToggleBreeding);
                
            if (showGeneticsButton != null)
                showGeneticsButton.onClick.AddListener(ToggleGeneticsPanel);
                
            if (photographyModeButton != null)
                photographyModeButton.onClick.AddListener(TogglePhotographyMode);
            
            // Market
            if (openMarketButton != null)
                openMarketButton.onClick.AddListener(ToggleMarketPanel);
            
            // Social
            if (socialHubButton != null)
                socialHubButton.onClick.AddListener(ToggleSocialPanel);
                
            if (visitFriendButton != null)
                visitFriendButton.onClick.AddListener(VisitRandomFriend);
            
            // Subscribe to game events
            if (economyManager != null)
            {
                economyManager.OnCoinsChanged += UpdateEconomyDisplay;
                economyManager.OnLevelUp += OnPlayerLevelUp;
            }
            
            if (fishManager != null)
            {
                fishManager.OnFishSpawned += OnFishSpawned;
                fishManager.OnFishBred += OnFishBred;
                fishManager.OnRareFishDiscovered += OnRareFishDiscovered;
            }
        }
        
        private void Update()
        {
            UpdateStatusDisplays();
            HandleFishSelection();
        }
        
        private void UpdateStatusDisplays()
        {
            // Update aquarium status
            if (aquariumStatusText != null && aquariumManager != null)
            {
                aquariumStatusText.text = aquariumManager.GetAquariumStatus();
            }
            
            // Update economy status
            if (economyStatusText != null && economyManager != null)
            {
                economyStatusText.text = economyManager.GetEconomyStatus();
            }
            
            // Update fish population
            if (fishPopulationText != null && fishManager != null)
            {
                fishPopulationText.text = fishManager.GetPopulationReport();
            }
            
            // Update breeding status
            if (breedingStatusText != null)
            {
                string status = isBreedingEnabled ? "Breeding: ENABLED" : "Breeding: DISABLED";
                if (fishManager != null)
                {
                    int breedableFish = CountBreedableFish();
                    status += $"\nBreedable Fish: {breedableFish}";
                }
                breedingStatusText.text = status;
            }
            
            // Update friends online (placeholder)
            if (friendsOnlineText != null)
            {
                friendsOnlineText.text = $"Friends Online: {Random.Range(0, 10)}"; // Placeholder
            }
        }
        
        private void HandleFishSelection()
        {
            // Simple fish selection with mouse click
            if (Input.GetMouseButtonDown(0))
            {
                Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
                RaycastHit hit;
                
                if (Physics.Raycast(ray, out hit))
                {
                    Fish.Fish fish = hit.collider.GetComponent<Fish.Fish>();
                    if (fish != null)
                    {
                        SelectFish(fish);
                    }
                }
            }
        }
        
        private void SelectFish(Fish.Fish fish)
        {
            selectedFish = fish;
            
            if (fishInfoPanel != null)
            {
                fishInfoPanel.SetActive(true);
                
                if (selectedFishName != null)
                    selectedFishName.text = fish.FishName;
                
                if (selectedFishStats != null)
                {
                    string stats = $"Species: {fish.Species?.speciesName ?? "Unknown"}\n";
                    stats += $"Age: {fish.Age:F1}s\n";
                    stats += $"Size: {fish.Size:F2}\n";
                    stats += $"Health: {fish.Health:F1}%\n";
                    stats += $"Happiness: {fish.Happiness:F1}%\n";
                    stats += $"Hunger: {fish.Hunger:F1}%\n";
                    stats += $"State: {fish.CurrentState}";
                    selectedFishStats.text = stats;
                }
                
                if (selectedFishGenetics != null && fish.Genetics != null)
                {
                    selectedFishGenetics.text = fish.Genetics.GetGeneticSummary();
                }
            }
        }
        
        // Button event handlers
        private void FeedAllFish()
        {
            aquariumManager?.FeedAllFish();
            ShowNotification("All fish have been fed!");
        }
        
        private void CleanAquarium()
        {
            aquariumManager?.CleanAquarium();
            ShowNotification("Aquarium cleaned!");
        }
        
        private void SpawnRandomFish()
        {
            if (fishManager != null)
            {
                var newFish = fishManager.SpawnRandomFish();
                if (newFish != null)
                {
                    ShowNotification($"New fish spawned: {newFish.FishName}!");
                }
                else
                {
                    ShowNotification("Cannot spawn fish - aquarium may be full!");
                }
            }
        }
        
        private void SaveGame()
        {
            gameManager?.SaveGame();
            ShowNotification("Game saved!");
        }
        
        private void OnGameSpeedChanged(float value)
        {
            gameManager?.SetGameSpeed(value);
            UpdateGameSpeedText();
        }
        
        private void UpdateGameSpeedText()
        {
            if (gameSpeedText != null && gameSpeedSlider != null)
            {
                gameSpeedText.text = $"Speed: {gameSpeedSlider.value:F1}x";
            }
        }
        
        // Advanced feature handlers
        private void ToggleBreeding()
        {
            isBreedingEnabled = !isBreedingEnabled;
            // This would communicate with the FishManager to enable/disable breeding
            
            if (breedingPanel != null)
                breedingPanel.SetActive(isBreedingEnabled);
                
            ShowNotification($"Breeding {(isBreedingEnabled ? "enabled" : "disabled")}!");
        }
        
        private void ToggleGeneticsPanel()
        {
            if (geneticsPanel != null)
            {
                bool isActive = !geneticsPanel.activeSelf;
                geneticsPanel.SetActive(isActive);
                
                if (isActive && selectedFish != null)
                {
                    // Display detailed genetics information
                    ShowNotification("Genetics panel opened - select a fish to see details!");
                }
            }
        }
        
        private void TogglePhotographyMode()
        {
            isPhotographyMode = !isPhotographyMode;
            
            if (photographyPanel != null)
                photographyPanel.SetActive(isPhotographyMode);
            
            // This would enable special camera controls and screenshot functionality
            ShowNotification($"Photography mode {(isPhotographyMode ? "enabled" : "disabled")}!");
        }
        
        private void ToggleMarketPanel()
        {
            if (marketPanel != null)
            {
                bool isActive = !marketPanel.activeSelf;
                marketPanel.SetActive(isActive);
                
                if (isActive)
                {
                    PopulateMarketItems();
                }
            }
        }
        
        private void ToggleSocialPanel()
        {
            if (socialPanel != null)
            {
                bool isActive = !socialPanel.activeSelf;
                socialPanel.SetActive(isActive);
                
                if (isActive)
                {
                    ShowNotification("Social hub opened - connect with friends!");
                }
            }
        }
        
        private void VisitRandomFriend()
        {
            // Placeholder for visiting friend's aquarium
            ShowNotification("Visiting friend's aquarium... (Feature coming soon!)");
        }
        
        // Event handlers
        private void OnFishSpawned(Fish.Fish fish)
        {
            ShowNotification($"New fish appeared: {fish.FishName}!");
        }
        
        private void OnFishBred(Fish.Fish parent1, Fish.Fish parent2, List<Fish.Fish> offspring)
        {
            ShowNotification($"Breeding success! {parent1.FishName} + {parent2.FishName} = {offspring.Count} babies!");
        }
        
        private void OnRareFishDiscovered(Fish.Fish rareFish)
        {
            ShowNotification($"RARE FISH DISCOVERED: {rareFish.FishName}!", 5f);
        }
        
        private void OnPlayerLevelUp(int newLevel)
        {
            ShowNotification($"LEVEL UP! You are now level {newLevel}!", 3f);
        }
        
        private void UpdateEconomyDisplay(int newCoins)
        {
            // Economy display is updated in UpdateStatusDisplays()
        }
        
        // Helper methods
        private int CountBreedableFish()
        {
            if (fishManager == null) return 0;
            
            int count = 0;
            foreach (var fish in fishManager.ManagedFish)
            {
                if (fish != null && fish.Age > 60f && fish.Health > 70f && fish.Happiness > 70f)
                {
                    count++;
                }
            }
            return count;
        }
        
        private void PopulateMarketItems()
        {
            // This would populate the market with available fish and items
            // Placeholder implementation
            ShowNotification("Market updated with new items!");
        }
        
        private void ShowNotification(string message, float duration = 2f)
        {
            // Simple notification system - in a full implementation this would show a proper notification UI
            Debug.Log($"[NOTIFICATION] {message}");
            
            // You could implement a proper notification popup here
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (economyManager != null)
            {
                economyManager.OnCoinsChanged -= UpdateEconomyDisplay;
                economyManager.OnLevelUp -= OnPlayerLevelUp;
            }
            
            if (fishManager != null)
            {
                fishManager.OnFishSpawned -= OnFishSpawned;
                fishManager.OnFishBred -= OnFishBred;
                fishManager.OnRareFishDiscovered -= OnRareFishDiscovered;
            }
        }
    }
}
