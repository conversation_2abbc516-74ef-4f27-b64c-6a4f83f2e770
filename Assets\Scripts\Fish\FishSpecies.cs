using UnityEngine;
using System.Collections.Generic;

namespace AquariumGame.Fish
{
    /// <summary>
    /// Defines different fish species with unique characteristics
    /// </summary>
    [CreateAssetMenu(fileName = "New Fish Species", menuName = "Aquarium Game/Fish Species")]
    public class FishSpecies : ScriptableObject
    {
        [Header("Basic Information")]
        public string speciesName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        public GameObject prefab;
        
        [Header("Physical Characteristics")]
        public float baseSize = 1.0f;
        public float sizeVariation = 0.2f; // ±20% size variation
        public Color[] possibleColors;
        public float baseSwimSpeed = 2.0f;
        public float speedVariation = 0.5f;
        
        [Header("Life Stats")]
        public float baseLifespan = 300f; // seconds
        public float lifespanVariation = 60f;
        public float baseHealth = 100f;
        public float healthVariation = 20f;
        
        [Header("Behavior")]
        public Fish.FishPersonality[] possiblePersonalities;
        public float baseSocialLevel = 0.5f;
        public float baseAggressionLevel = 0.2f;
        public float baseCuriosityLevel = 0.7f;
        
        [Header("Breeding")]
        public bool canBreed = true;
        public float breedingCooldown = 120f; // seconds
        public int minBreedingAge = 60; // seconds
        public FishSpecies[] compatibleSpecies; // Can breed with these species
        public float hybridChance = 0.1f; // 10% chance for hybrid offspring
        
        [Header("Economic")]
        public int baseSellPrice = 10;
        public int baseBuyPrice = 15;
        public Rarity rarity = Rarity.Common;
        
        [Header("Special Abilities")]
        public SpecialAbility[] specialAbilities;
        
        [Header("Environmental Preferences")]
        public float preferredTemperature = 24f; // Celsius
        public float temperatureTolerance = 5f;
        public float preferredPH = 7.0f;
        public float pHTolerance = 1.0f;
        public WaterType preferredWaterType = WaterType.Freshwater;
        
        [Header("Feeding")]
        public FoodType[] preferredFoods;
        public float feedingFrequency = 0.1f; // How often they need food (per second)
        public float maxHunger = 100f;
        
        public enum Rarity
        {
            Common,
            Uncommon,
            Rare,
            Epic,
            Legendary,
            Mythical
        }
        
        public enum WaterType
        {
            Freshwater,
            Saltwater,
            Brackish
        }
        
        public enum FoodType
        {
            Flakes,
            Pellets,
            LiveFood,
            Vegetables,
            Meat,
            Plankton,
            Algae
        }
        
        [System.Serializable]
        public class SpecialAbility
        {
            public string abilityName;
            [TextArea(2, 3)]
            public string description;
            public float cooldown = 30f;
            public AbilityType type;
            public float effectStrength = 1.0f;
            
            public enum AbilityType
            {
                Healing,        // Heals nearby fish
                Cleaning,       // Cleans aquarium
                Attraction,     // Attracts other fish
                Camouflage,     // Becomes invisible
                Bioluminescence, // Glows in the dark
                Telepathy,      // Communicates with player
                TimeSlowing,    // Slows down time around them
                Breeding,       // Increases breeding success
                Growth,         // Grows faster
                Longevity      // Lives longer
            }
        }
        
        // Methods for generating fish with species characteristics
        public Fish.FishPersonality GetRandomPersonality()
        {
            if (possiblePersonalities.Length == 0)
                return Fish.FishPersonality.Peaceful;
                
            return possiblePersonalities[Random.Range(0, possiblePersonalities.Length)];
        }
        
        public Color GetRandomColor()
        {
            if (possibleColors.Length == 0)
                return Color.white;
                
            return possibleColors[Random.Range(0, possibleColors.Length)];
        }
        
        public float GetRandomSize()
        {
            return baseSize + Random.Range(-sizeVariation, sizeVariation);
        }
        
        public float GetRandomSwimSpeed()
        {
            return baseSwimSpeed + Random.Range(-speedVariation, speedVariation);
        }
        
        public float GetRandomLifespan()
        {
            return baseLifespan + Random.Range(-lifespanVariation, lifespanVariation);
        }
        
        public float GetRandomHealth()
        {
            return baseHealth + Random.Range(-healthVariation, healthVariation);
        }
        
        public bool IsCompatibleWith(FishSpecies otherSpecies)
        {
            if (!canBreed || !otherSpecies.canBreed)
                return false;
                
            // Same species can always breed
            if (this == otherSpecies)
                return true;
                
            // Check compatibility list
            foreach (FishSpecies compatible in compatibleSpecies)
            {
                if (compatible == otherSpecies)
                    return true;
            }
            
            return false;
        }
        
        public int GetSellPrice(float fishSize, float fishAge, List<string> rareTraits)
        {
            float price = baseSellPrice;
            
            // Size modifier
            price *= fishSize;
            
            // Age modifier (older fish are worth more, up to a point)
            float ageRatio = fishAge / baseLifespan;
            if (ageRatio < 0.5f)
                price *= (1f + ageRatio);
            else
                price *= (1.5f - ageRatio * 0.5f); // Diminishing returns for very old fish
            
            // Rare traits multiplier
            foreach (string trait in rareTraits)
            {
                price *= 1.5f; // Each rare trait increases value by 50%
            }
            
            // Rarity multiplier
            switch (rarity)
            {
                case Rarity.Uncommon: price *= 2f; break;
                case Rarity.Rare: price *= 5f; break;
                case Rarity.Epic: price *= 10f; break;
                case Rarity.Legendary: price *= 25f; break;
                case Rarity.Mythical: price *= 100f; break;
            }
            
            return Mathf.RoundToInt(price);
        }
        
        public bool CanSurviveInEnvironment(float temperature, float pH, WaterType waterType)
        {
            bool temperatureOK = Mathf.Abs(temperature - preferredTemperature) <= temperatureTolerance;
            bool pHOK = Mathf.Abs(pH - preferredPH) <= pHTolerance;
            bool waterTypeOK = waterType == preferredWaterType;
            
            return temperatureOK && pHOK && waterTypeOK;
        }
        
        public float GetEnvironmentStress(float temperature, float pH, WaterType waterType)
        {
            float stress = 0f;
            
            // Temperature stress
            float tempDiff = Mathf.Abs(temperature - preferredTemperature);
            if (tempDiff > temperatureTolerance)
                stress += (tempDiff - temperatureTolerance) / temperatureTolerance;
            
            // pH stress
            float pHDiff = Mathf.Abs(pH - preferredPH);
            if (pHDiff > pHTolerance)
                stress += (pHDiff - pHTolerance) / pHTolerance;
            
            // Water type stress
            if (waterType != preferredWaterType)
                stress += 0.5f;
            
            return Mathf.Clamp01(stress);
        }
        
        public string GetSpeciesInfo()
        {
            string info = $"<b>{speciesName}</b>\n";
            info += $"{description}\n\n";
            info += $"<b>Characteristics:</b>\n";
            info += $"Size: {baseSize:F1} (±{sizeVariation:F1})\n";
            info += $"Speed: {baseSwimSpeed:F1} (±{speedVariation:F1})\n";
            info += $"Lifespan: {baseLifespan:F0}s (±{lifespanVariation:F0}s)\n";
            info += $"Rarity: {rarity}\n\n";
            
            if (specialAbilities.Length > 0)
            {
                info += $"<b>Special Abilities:</b>\n";
                foreach (var ability in specialAbilities)
                {
                    info += $"• {ability.abilityName}: {ability.description}\n";
                }
                info += "\n";
            }
            
            info += $"<b>Environment:</b>\n";
            info += $"Temperature: {preferredTemperature:F1}°C (±{temperatureTolerance:F1}°C)\n";
            info += $"pH: {preferredPH:F1} (±{pHTolerance:F1})\n";
            info += $"Water Type: {preferredWaterType}\n";
            
            return info;
        }
    }
}
