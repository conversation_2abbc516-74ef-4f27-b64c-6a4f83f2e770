using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace AquariumGame.Aquarium
{
    /// <summary>
    /// Manages the aquarium environment and all fish within it
    /// </summary>
    public class AquariumManager : MonoBehaviour
    {
        [Header("Aquarium Properties")]
        [SerializeField] private float volume = 100f; // Liters
        [SerializeField] private float temperature = 24f; // Celsius
        [SerializeField] private float pH = 7.0f;
        [SerializeField] private Fish.FishSpecies.WaterType waterType = Fish.FishSpecies.WaterType.Freshwater;
        
        [Header("Environment")]
        [SerializeField] private float oxygenLevel = 100f;
        [SerializeField] private float cleanlinessLevel = 100f;
        [SerializeField] private float lightLevel = 80f;
        [SerializeField] private bool hasFilter = true;
        [SerializeField] private bool hasHeater = true;
        [SerializeField] private bool hasAerator = true;
        
        [Header("Boundaries")]
        [SerializeField] private Bounds aquariumBounds = new Bounds(Vector3.zero, new Vector3(10, 6, 10));
        [SerializeField] private Transform[] decorations;
        [SerializeField] private Transform[] plants;
        
        [Header("Fish Management")]
        [SerializeField] private List<Fish.Fish> fishInAquarium = new List<Fish.Fish>();
        [SerializeField] private int maxFishCapacity = 20;
        [SerializeField] private Transform fishSpawnPoint;
        
        [Header("Feeding")]
        [SerializeField] private float lastFeedingTime;
        [SerializeField] private float autoFeedingInterval = 300f; // 5 minutes
        [SerializeField] private bool autoFeedingEnabled = false;
        
        // Events
        public System.Action<Fish.Fish> OnFishAdded;
        public System.Action<Fish.Fish> OnFishRemoved;
        public System.Action<float> OnTemperatureChanged;
        public System.Action<float> OnCleanlinessChanged;
        public System.Action OnEnvironmentUpdated;
        
        // Properties
        public float Volume => volume;
        public float Temperature => temperature;
        public float PH => pH;
        public Fish.FishSpecies.WaterType WaterType => waterType;
        public float OxygenLevel => oxygenLevel;
        public float CleanlinessLevel => cleanlinessLevel;
        public float LightLevel => lightLevel;
        public List<Fish.Fish> FishInAquarium => fishInAquarium;
        public int FishCount => fishInAquarium.Count;
        public int MaxCapacity => maxFishCapacity;
        public Bounds AquariumBounds => aquariumBounds;
        
        // Internal state
        private float environmentUpdateTimer = 0f;
        private const float ENVIRONMENT_UPDATE_INTERVAL = 1f; // Update every second
        
        public void Initialize()
        {
            // Set up initial environment
            UpdateEnvironment();
            
            // Subscribe to fish events
            foreach (var fish in fishInAquarium)
            {
                SubscribeToFishEvents(fish);
            }
            
            Debug.Log($"[AquariumManager] Initialized with {fishInAquarium.Count} fish");
        }
        
        private void Update()
        {
            environmentUpdateTimer += Time.deltaTime;
            
            if (environmentUpdateTimer >= ENVIRONMENT_UPDATE_INTERVAL)
            {
                UpdateEnvironment();
                environmentUpdateTimer = 0f;
            }
            
            // Auto feeding
            if (autoFeedingEnabled && Time.time - lastFeedingTime > autoFeedingInterval)
            {
                FeedAllFish();
            }
        }
        
        private void UpdateEnvironment()
        {
            // Calculate environmental changes based on fish load and equipment
            float fishLoad = (float)fishInAquarium.Count / maxFishCapacity;
            
            // Oxygen consumption
            float oxygenConsumption = fishLoad * 2f * Time.deltaTime;
            if (hasAerator)
                oxygenConsumption *= 0.5f; // Aerator reduces consumption
                
            oxygenLevel = Mathf.Max(0f, oxygenLevel - oxygenConsumption);
            
            // Add oxygen from plants
            float oxygenProduction = plants.Length * 0.5f * Time.deltaTime;
            oxygenLevel = Mathf.Min(100f, oxygenLevel + oxygenProduction);
            
            // Cleanliness degradation
            float cleanlinessLoss = fishLoad * 1.5f * Time.deltaTime;
            if (hasFilter)
                cleanlinessLoss *= 0.3f; // Filter greatly reduces cleanliness loss
                
            cleanlinessLevel = Mathf.Max(0f, cleanlinessLevel - cleanlinessLoss);
            
            // Temperature regulation
            if (hasHeater)
            {
                // Heater maintains target temperature
                float targetTemp = 24f; // Could be adjustable
                temperature = Mathf.MoveTowards(temperature, targetTemp, 2f * Time.deltaTime);
            }
            else
            {
                // Temperature drifts toward room temperature
                float roomTemp = 20f;
                temperature = Mathf.MoveTowards(temperature, roomTemp, 0.5f * Time.deltaTime);
            }
            
            // pH changes based on fish waste and plants
            float pHChange = (fishLoad * 0.1f - plants.Length * 0.05f) * Time.deltaTime;
            pH = Mathf.Clamp(pH + pHChange, 5f, 9f);
            
            // Apply environmental stress to fish
            ApplyEnvironmentalEffects();
            
            OnEnvironmentUpdated?.Invoke();
        }
        
        private void ApplyEnvironmentalEffects()
        {
            foreach (var fish in fishInAquarium)
            {
                if (fish == null) continue;
                
                float environmentalStress = 0f;
                
                // Low oxygen stress
                if (oxygenLevel < 50f)
                    environmentalStress += (50f - oxygenLevel) / 50f;
                
                // Poor cleanliness stress
                if (cleanlinessLevel < 30f)
                    environmentalStress += (30f - cleanlinessLevel) / 30f;
                
                // Species-specific environmental stress
                if (fish.Species != null)
                {
                    environmentalStress += fish.Species.GetEnvironmentStress(temperature, pH, waterType);
                }
                
                // Apply stress effects (this would be implemented in the Fish class)
                // fish.ApplyEnvironmentalStress(environmentalStress);
            }
        }
        
        public bool AddFish(Fish.Fish fish)
        {
            if (fishInAquarium.Count >= maxFishCapacity)
            {
                Debug.LogWarning("[AquariumManager] Cannot add fish: aquarium at capacity");
                return false;
            }
            
            if (fish == null)
            {
                Debug.LogError("[AquariumManager] Cannot add null fish");
                return false;
            }
            
            // Check if fish can survive in current environment
            if (fish.Species != null && !fish.Species.CanSurviveInEnvironment(temperature, pH, waterType))
            {
                Debug.LogWarning($"[AquariumManager] Fish {fish.FishName} cannot survive in current environment");
                return false;
            }
            
            fishInAquarium.Add(fish);
            SubscribeToFishEvents(fish);
            
            // Position fish in aquarium
            PositionFishInAquarium(fish);
            
            OnFishAdded?.Invoke(fish);
            Debug.Log($"[AquariumManager] Added fish: {fish.FishName}");
            
            return true;
        }
        
        public bool RemoveFish(Fish.Fish fish)
        {
            if (fishInAquarium.Remove(fish))
            {
                UnsubscribeFromFishEvents(fish);
                OnFishRemoved?.Invoke(fish);
                Debug.Log($"[AquariumManager] Removed fish: {fish.FishName}");
                return true;
            }
            
            return false;
        }
        
        private void PositionFishInAquarium(Fish.Fish fish)
        {
            // Position fish randomly within aquarium bounds
            Vector3 randomPosition = new Vector3(
                Random.Range(aquariumBounds.min.x, aquariumBounds.max.x),
                Random.Range(aquariumBounds.min.y, aquariumBounds.max.y),
                Random.Range(aquariumBounds.min.z, aquariumBounds.max.z)
            );
            
            fish.transform.position = randomPosition;
        }
        
        private void SubscribeToFishEvents(Fish.Fish fish)
        {
            fish.OnFishDied += HandleFishDeath;
            fish.OnFishInteraction += HandleFishInteraction;
        }
        
        private void UnsubscribeFromFishEvents(Fish.Fish fish)
        {
            fish.OnFishDied -= HandleFishDeath;
            fish.OnFishInteraction -= HandleFishInteraction;
        }
        
        private void HandleFishDeath(Fish.Fish deadFish)
        {
            RemoveFish(deadFish);
            
            // Dead fish pollute the water
            cleanlinessLevel = Mathf.Max(0f, cleanlinessLevel - 10f);
            OnCleanlinessChanged?.Invoke(cleanlinessLevel);
        }
        
        private void HandleFishInteraction(Fish.Fish fish1, Fish.Fish fish2)
        {
            // Handle fish interactions (breeding, fighting, etc.)
            Debug.Log($"[AquariumManager] Fish interaction: {fish1.FishName} and {fish2.FishName}");
        }
        
        public void FeedAllFish(float nutritionValue = 20f)
        {
            foreach (var fish in fishInAquarium)
            {
                fish.Feed(nutritionValue);
            }
            
            lastFeedingTime = Time.time;
            
            // Feeding slightly reduces cleanliness
            cleanlinessLevel = Mathf.Max(0f, cleanlinessLevel - 2f);
            
            Debug.Log($"[AquariumManager] Fed all {fishInAquarium.Count} fish");
        }
        
        public void CleanAquarium()
        {
            cleanlinessLevel = 100f;
            OnCleanlinessChanged?.Invoke(cleanlinessLevel);
            Debug.Log("[AquariumManager] Aquarium cleaned");
        }
        
        public void SetTemperature(float newTemperature)
        {
            temperature = Mathf.Clamp(newTemperature, 15f, 35f);
            OnTemperatureChanged?.Invoke(temperature);
        }
        
        public void SetAutoFeeding(bool enabled)
        {
            autoFeedingEnabled = enabled;
            Debug.Log($"[AquariumManager] Auto feeding {(enabled ? "enabled" : "disabled")}");
        }
        
        public Vector3 GetRandomPositionInAquarium()
        {
            return new Vector3(
                Random.Range(aquariumBounds.min.x, aquariumBounds.max.x),
                Random.Range(aquariumBounds.min.y, aquariumBounds.max.y),
                Random.Range(aquariumBounds.min.z, aquariumBounds.max.z)
            );
        }
        
        public List<Fish.Fish> GetFishBySpecies(Fish.FishSpecies species)
        {
            return fishInAquarium.Where(f => f.Species == species).ToList();
        }
        
        public float GetAquariumHealth()
        {
            // Calculate overall aquarium health based on various factors
            float health = 0f;
            
            health += oxygenLevel * 0.3f;
            health += cleanlinessLevel * 0.4f;
            health += (100f - Mathf.Abs(temperature - 24f) * 10f) * 0.2f; // Optimal temp is 24°C
            health += (100f - Mathf.Abs(pH - 7f) * 20f) * 0.1f; // Optimal pH is 7
            
            return Mathf.Clamp(health, 0f, 100f);
        }
        
        public string GetAquariumStatus()
        {
            string status = $"<b>Aquarium Status</b>\n";
            status += $"Fish: {fishInAquarium.Count}/{maxFishCapacity}\n";
            status += $"Health: {GetAquariumHealth():F1}%\n";
            status += $"Temperature: {temperature:F1}°C\n";
            status += $"pH: {pH:F1}\n";
            status += $"Oxygen: {oxygenLevel:F1}%\n";
            status += $"Cleanliness: {cleanlinessLevel:F1}%\n";
            status += $"Light: {lightLevel:F1}%\n";
            
            return status;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw aquarium bounds in editor
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireCube(aquariumBounds.center, aquariumBounds.size);
        }
    }
}
