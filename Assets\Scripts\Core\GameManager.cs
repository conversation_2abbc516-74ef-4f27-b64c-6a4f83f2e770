using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace AquariumGame.Core
{
    /// <summary>
    /// Main game manager that handles core game systems and state management
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private bool debugMode = false;
        [SerializeField] private float gameSpeed = 1.0f;
        
        [Header("Systems")]
        [SerializeField] private AquariumManager aquariumManager;
        [SerializeField] private FishManager fishManager;
        [SerializeField] private EconomyManager economyManager;
        [SerializeField] private SaveManager saveManager;
        
        // Singleton pattern
        public static GameManager Instance { get; private set; }
        
        // Game state
        public enum GameState
        {
            Loading,
            MainMenu,
            Playing,
            Paused,
            Settings,
            Social
        }
        
        [SerializeField] private GameState currentState = GameState.Loading;
        public GameState CurrentState => currentState;
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action OnGameLoaded;
        public System.Action OnGameSaved;
        
        // Properties
        public float GameSpeed => gameSpeed;
        public bool IsDebugMode => debugMode;
        public AquariumManager AquariumManager => aquariumManager;
        public FishManager FishManager => fishManager;
        public EconomyManager EconomyManager => economyManager;
        public SaveManager SaveManager => saveManager;
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            StartCoroutine(LoadGameData());
        }
        
        private void InitializeGame()
        {
            // Initialize core systems
            if (debugMode)
                Debug.Log("[GameManager] Initializing game systems...");
                
            // Set up quality settings
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
            
            // Initialize managers if not assigned
            if (aquariumManager == null)
                aquariumManager = FindObjectOfType<AquariumManager>();
            if (fishManager == null)
                fishManager = FindObjectOfType<FishManager>();
            if (economyManager == null)
                economyManager = FindObjectOfType<EconomyManager>();
            if (saveManager == null)
                saveManager = FindObjectOfType<SaveManager>();
        }
        
        private IEnumerator LoadGameData()
        {
            SetGameState(GameState.Loading);
            
            // Load save data
            if (saveManager != null)
            {
                yield return StartCoroutine(saveManager.LoadGame());
            }
            
            // Initialize systems with loaded data
            yield return StartCoroutine(InitializeSystems());
            
            // Game is ready
            SetGameState(GameState.MainMenu);
            OnGameLoaded?.Invoke();
            
            if (debugMode)
                Debug.Log("[GameManager] Game loaded successfully!");
        }
        
        private IEnumerator InitializeSystems()
        {
            // Initialize each system
            if (economyManager != null)
            {
                economyManager.Initialize();
                yield return null;
            }
            
            if (aquariumManager != null)
            {
                aquariumManager.Initialize();
                yield return null;
            }
            
            if (fishManager != null)
            {
                fishManager.Initialize();
                yield return null;
            }
        }
        
        public void SetGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            if (debugMode)
                Debug.Log($"[GameManager] State changed: {previousState} -> {newState}");
            
            OnGameStateChanged?.Invoke(newState);
            
            // Handle state-specific logic
            switch (newState)
            {
                case GameState.Playing:
                    Time.timeScale = gameSpeed;
                    break;
                case GameState.Paused:
                    Time.timeScale = 0f;
                    break;
                default:
                    Time.timeScale = 1f;
                    break;
            }
        }
        
        public void PauseGame()
        {
            SetGameState(GameState.Paused);
        }
        
        public void ResumeGame()
        {
            SetGameState(GameState.Playing);
        }
        
        public void SetGameSpeed(float speed)
        {
            gameSpeed = Mathf.Clamp(speed, 0.1f, 5.0f);
            if (currentState == GameState.Playing)
                Time.timeScale = gameSpeed;
        }
        
        public void SaveGame()
        {
            if (saveManager != null)
            {
                StartCoroutine(SaveGameCoroutine());
            }
        }
        
        private IEnumerator SaveGameCoroutine()
        {
            yield return StartCoroutine(saveManager.SaveGame());
            OnGameSaved?.Invoke();
            
            if (debugMode)
                Debug.Log("[GameManager] Game saved successfully!");
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.Playing)
            {
                SaveGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.Playing)
            {
                SaveGame();
            }
        }
        
        private void OnDestroy()
        {
            if (Instance == this)
            {
                SaveGame();
            }
        }
    }
}
