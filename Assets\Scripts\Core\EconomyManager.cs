using UnityEngine;
using System.Collections.Generic;

namespace AquariumGame.Core
{
    /// <summary>
    /// Manages the game's economy, currency, and transactions
    /// </summary>
    public class EconomyManager : MonoBehaviour
    {
        [Header("Currency")]
        [SerializeField] private int coins = 100;
        [SerializeField] private int gems = 10;
        [SerializeField] private int experience = 0;
        [SerializeField] private int level = 1;
        
        [Header("Income Sources")]
        [SerializeField] private float passiveIncomeRate = 1f; // coins per second
        [SerializeField] private float lastIncomeTime;
        [SerializeField] private bool offlineProgressEnabled = true;
        
        [Header("Shop Items")]
        [SerializeField] private List<ShopItem> availableItems = new List<ShopItem>();
        [SerializeField] private List<Fish.FishSpecies> availableFishSpecies = new List<Fish.FishSpecies>();
        
        [Header("Market Prices")]
        [SerializeField] private Dictionary<string, MarketPrice> marketPrices = new Dictionary<string, MarketPrice>();
        [SerializeField] private float priceFluctuationRate = 0.1f; // 10% price changes
        
        // Events
        public System.Action<int> OnCoinsChanged;
        public System.Action<int> OnGemsChanged;
        public System.Action<int> OnExperienceChanged;
        public System.Action<int> OnLevelUp;
        public System.Action<ShopItem> OnItemPurchased;
        public System.Action<Fish.Fish> OnFishSold;
        
        // Properties
        public int Coins => coins;
        public int Gems => gems;
        public int Experience => experience;
        public int Level => level;
        public float PassiveIncomeRate => passiveIncomeRate;
        
        [System.Serializable]
        public class ShopItem
        {
            public string itemName;
            public string description;
            public Sprite icon;
            public int coinPrice;
            public int gemPrice;
            public ItemType type;
            public bool isUnlocked = true;
            public int requiredLevel = 1;
            
            public enum ItemType
            {
                Fish,
                Decoration,
                Equipment,
                Food,
                Upgrade,
                Special
            }
        }
        
        [System.Serializable]
        public class MarketPrice
        {
            public float basePrice;
            public float currentPrice;
            public float priceHistory;
            public float volatility = 0.1f;
            
            public MarketPrice(float price)
            {
                basePrice = price;
                currentPrice = price;
                priceHistory = price;
            }
        }
        
        public void Initialize()
        {
            lastIncomeTime = Time.time;
            InitializeMarketPrices();
            CalculateOfflineProgress();
            
            Debug.Log($"[EconomyManager] Initialized - Coins: {coins}, Gems: {gems}, Level: {level}");
        }
        
        private void Update()
        {
            // Generate passive income
            GeneratePassiveIncome();
            
            // Update market prices periodically
            UpdateMarketPrices();
        }
        
        private void GeneratePassiveIncome()
        {
            float currentTime = Time.time;
            float timeDelta = currentTime - lastIncomeTime;
            
            if (timeDelta >= 1f) // Generate income every second
            {
                int incomeAmount = Mathf.FloorToInt(passiveIncomeRate * timeDelta);
                if (incomeAmount > 0)
                {
                    AddCoins(incomeAmount);
                }
                
                lastIncomeTime = currentTime;
            }
        }
        
        private void CalculateOfflineProgress()
        {
            if (!offlineProgressEnabled) return;
            
            // Calculate offline time (this would come from save data)
            float offlineTime = 0f; // Placeholder - would be calculated from last save time
            
            if (offlineTime > 0f)
            {
                int offlineIncome = Mathf.FloorToInt(passiveIncomeRate * offlineTime);
                
                // Cap offline income to prevent exploitation
                int maxOfflineIncome = Mathf.FloorToInt(passiveIncomeRate * 3600f); // Max 1 hour worth
                offlineIncome = Mathf.Min(offlineIncome, maxOfflineIncome);
                
                if (offlineIncome > 0)
                {
                    AddCoins(offlineIncome);
                    Debug.Log($"[EconomyManager] Offline income: {offlineIncome} coins");
                }
            }
        }
        
        private void InitializeMarketPrices()
        {
            // Initialize market prices for different fish species
            foreach (var species in availableFishSpecies)
            {
                if (!marketPrices.ContainsKey(species.name))
                {
                    marketPrices[species.name] = new MarketPrice(species.baseSellPrice);
                }
            }
        }
        
        private void UpdateMarketPrices()
        {
            // Update market prices with some randomness (simplified market simulation)
            if (Random.Range(0f, 1f) < 0.01f) // 1% chance per frame
            {
                foreach (var kvp in marketPrices)
                {
                    MarketPrice price = kvp.Value;
                    float change = Random.Range(-price.volatility, price.volatility);
                    price.currentPrice = Mathf.Max(0.1f, price.currentPrice * (1f + change));
                    
                    // Gradually return to base price
                    price.currentPrice = Mathf.Lerp(price.currentPrice, price.basePrice, 0.01f);
                }
            }
        }
        
        public bool CanAfford(int coinCost, int gemCost = 0)
        {
            return coins >= coinCost && gems >= gemCost;
        }
        
        public bool SpendCurrency(int coinCost, int gemCost = 0)
        {
            if (!CanAfford(coinCost, gemCost))
                return false;
            
            coins -= coinCost;
            gems -= gemCost;
            
            OnCoinsChanged?.Invoke(coins);
            if (gemCost > 0)
                OnGemsChanged?.Invoke(gems);
            
            return true;
        }
        
        public void AddCoins(int amount)
        {
            coins += amount;
            OnCoinsChanged?.Invoke(coins);
        }
        
        public void AddGems(int amount)
        {
            gems += amount;
            OnGemsChanged?.Invoke(gems);
        }
        
        public void AddExperience(int amount)
        {
            experience += amount;
            OnExperienceChanged?.Invoke(experience);
            
            // Check for level up
            int newLevel = CalculateLevel(experience);
            if (newLevel > level)
            {
                level = newLevel;
                OnLevelUp?.Invoke(level);
                
                // Level up rewards
                AddCoins(level * 10);
                AddGems(level);
                
                Debug.Log($"[EconomyManager] Level up! New level: {level}");
            }
        }
        
        private int CalculateLevel(int exp)
        {
            // Simple level calculation: level = sqrt(experience / 100) + 1
            return Mathf.FloorToInt(Mathf.Sqrt(exp / 100f)) + 1;
        }
        
        public int GetExperienceForNextLevel()
        {
            int nextLevel = level + 1;
            int expForNextLevel = (nextLevel - 1) * (nextLevel - 1) * 100;
            return expForNextLevel - experience;
        }
        
        public bool PurchaseItem(ShopItem item)
        {
            if (!CanPurchaseItem(item))
                return false;
            
            if (SpendCurrency(item.coinPrice, item.gemPrice))
            {
                OnItemPurchased?.Invoke(item);
                AddExperience(10); // Gain XP for purchases
                
                Debug.Log($"[EconomyManager] Purchased: {item.itemName}");
                return true;
            }
            
            return false;
        }
        
        public bool CanPurchaseItem(ShopItem item)
        {
            return item.isUnlocked && 
                   level >= item.requiredLevel && 
                   CanAfford(item.coinPrice, item.gemPrice);
        }
        
        public int SellFish(Fish.Fish fish)
        {
            if (fish == null || fish.Species == null)
                return 0;
            
            // Calculate sell price based on fish properties
            List<string> rareTraits = fish.Genetics?.GetAllRareTraits() ?? new List<string>();
            int sellPrice = fish.Species.GetSellPrice(fish.Size, fish.Age, rareTraits);
            
            // Apply market price modifier
            if (marketPrices.ContainsKey(fish.Species.name))
            {
                float priceModifier = marketPrices[fish.Species.name].currentPrice / marketPrices[fish.Species.name].basePrice;
                sellPrice = Mathf.RoundToInt(sellPrice * priceModifier);
            }
            
            AddCoins(sellPrice);
            AddExperience(5); // Gain XP for selling fish
            
            OnFishSold?.Invoke(fish);
            
            Debug.Log($"[EconomyManager] Sold {fish.FishName} for {sellPrice} coins");
            return sellPrice;
        }
        
        public Fish.FishSpecies GetRandomFishSpeciesForPurchase()
        {
            // Filter species by level requirement and availability
            List<Fish.FishSpecies> availableSpecies = new List<Fish.FishSpecies>();
            
            foreach (var species in availableFishSpecies)
            {
                // Add level-based availability logic here
                availableSpecies.Add(species);
            }
            
            if (availableSpecies.Count == 0)
                return null;
            
            return availableSpecies[Random.Range(0, availableSpecies.Count)];
        }
        
        public void UnlockItem(string itemName)
        {
            ShopItem item = availableItems.Find(i => i.itemName == itemName);
            if (item != null)
            {
                item.isUnlocked = true;
                Debug.Log($"[EconomyManager] Unlocked item: {itemName}");
            }
        }
        
        public float GetMarketPrice(string itemName)
        {
            if (marketPrices.ContainsKey(itemName))
                return marketPrices[itemName].currentPrice;
            
            return 0f;
        }
        
        public void SetPassiveIncomeRate(float newRate)
        {
            passiveIncomeRate = newRate;
            Debug.Log($"[EconomyManager] Passive income rate set to: {newRate} coins/second");
        }
        
        public string GetEconomyStatus()
        {
            string status = $"<b>Economy Status</b>\n";
            status += $"Level: {level}\n";
            status += $"Coins: {coins:N0}\n";
            status += $"Gems: {gems:N0}\n";
            status += $"Experience: {experience:N0}\n";
            status += $"Next Level: {GetExperienceForNextLevel():N0} XP\n";
            status += $"Passive Income: {passiveIncomeRate:F1} coins/sec\n";
            
            return status;
        }
        
        // Save/Load methods (would be called by SaveManager)
        public EconomySaveData GetSaveData()
        {
            return new EconomySaveData
            {
                coins = this.coins,
                gems = this.gems,
                experience = this.experience,
                level = this.level,
                lastIncomeTime = this.lastIncomeTime,
                passiveIncomeRate = this.passiveIncomeRate
            };
        }
        
        public void LoadSaveData(EconomySaveData data)
        {
            coins = data.coins;
            gems = data.gems;
            experience = data.experience;
            level = data.level;
            lastIncomeTime = data.lastIncomeTime;
            passiveIncomeRate = data.passiveIncomeRate;
            
            // Trigger events to update UI
            OnCoinsChanged?.Invoke(coins);
            OnGemsChanged?.Invoke(gems);
            OnExperienceChanged?.Invoke(experience);
        }
        
        [System.Serializable]
        public class EconomySaveData
        {
            public int coins;
            public int gems;
            public int experience;
            public int level;
            public float lastIncomeTime;
            public float passiveIncomeRate;
        }
    }
}
