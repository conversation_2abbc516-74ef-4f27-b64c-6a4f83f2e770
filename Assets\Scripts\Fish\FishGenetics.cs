using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace AquariumGame.Fish
{
    /// <summary>
    /// Advanced genetics system for fish breeding - a major innovation beyond Tiny Aquarium
    /// </summary>
    [System.Serializable]
    public class FishGenetics
    {
        [Header("Genetic Traits")]
        [SerializeField] private Dictionary<string, Gene> genes;
        [SerializeField] private float mutationRate = 0.05f; // 5% chance of mutation
        
        // Genetic trait categories
        public enum TraitCategory
        {
            Physical,    // Size, color, fin shape
            Behavioral,  // Aggression, social, curiosity
            Performance, // Speed, health, lifespan
            Special      // Rare traits, unique abilities
        }
        
        [System.Serializable]
        public class Gene
        {
            public string traitName;
            public TraitCategory category;
            public float dominantValue;
            public float recessiveValue;
            public bool isDominant;
            public float expressionStrength = 1.0f; // How strongly this gene is expressed
            public bool isRare = false;
            
            public Gene(string name, TraitCategory cat, float dominant, float recessive, bool dom = true)
            {
                traitName = name;
                category = cat;
                dominantValue = dominant;
                recessiveValue = recessive;
                isDominant = dom;
            }
            
            public float GetExpressedValue()
            {
                float baseValue = isDominant ? dominantValue : recessiveValue;
                return baseValue * expressionStrength;
            }
        }
        
        public FishGenetics()
        {
            InitializeDefaultGenes();
        }
        
        public FishGenetics(FishGenetics parent1, FishGenetics parent2)
        {
            genes = new Dictionary<string, Gene>();
            BreedFromParents(parent1, parent2);
        }
        
        private void InitializeDefaultGenes()
        {
            genes = new Dictionary<string, Gene>();
            
            // Physical traits
            AddGene("size", TraitCategory.Physical, 1.2f, 0.8f, Random.Range(0f, 1f) > 0.5f);
            AddGene("primaryColorR", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            AddGene("primaryColorG", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            AddGene("primaryColorB", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            AddGene("secondaryColorR", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            AddGene("secondaryColorG", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            AddGene("secondaryColorB", TraitCategory.Physical, Random.Range(0f, 1f), Random.Range(0f, 1f));
            
            // Behavioral traits
            AddGene("aggression", TraitCategory.Behavioral, 0.8f, 0.2f, Random.Range(0f, 1f) > 0.7f);
            AddGene("social", TraitCategory.Behavioral, 0.9f, 0.3f, Random.Range(0f, 1f) > 0.5f);
            AddGene("curiosity", TraitCategory.Behavioral, 0.8f, 0.4f, Random.Range(0f, 1f) > 0.6f);
            AddGene("intelligence", TraitCategory.Behavioral, 0.9f, 0.5f, Random.Range(0f, 1f) > 0.8f);
            
            // Performance traits
            AddGene("speed", TraitCategory.Performance, 3.5f, 1.5f, Random.Range(0f, 1f) > 0.6f);
            AddGene("health", TraitCategory.Performance, 120f, 80f, Random.Range(0f, 1f) > 0.5f);
            AddGene("lifespan", TraitCategory.Performance, 400f, 200f, Random.Range(0f, 1f) > 0.7f);
            AddGene("fertility", TraitCategory.Performance, 0.9f, 0.5f, Random.Range(0f, 1f) > 0.5f);
            
            // Special traits (rare)
            if (Random.Range(0f, 1f) < 0.1f) // 10% chance for special traits
            {
                AddRareGene("bioluminescence", TraitCategory.Special, 1.0f, 0.0f);
            }
            
            if (Random.Range(0f, 1f) < 0.05f) // 5% chance
            {
                AddRareGene("telepathy", TraitCategory.Special, 1.0f, 0.0f);
            }
            
            if (Random.Range(0f, 1f) < 0.03f) // 3% chance
            {
                AddRareGene("shapeshifting", TraitCategory.Special, 1.0f, 0.0f);
            }
        }
        
        private void AddGene(string traitName, TraitCategory category, float dominant, float recessive, bool isDominant = true)
        {
            Gene newGene = new Gene(traitName, category, dominant, recessive, isDominant);
            genes[traitName] = newGene;
        }
        
        private void AddRareGene(string traitName, TraitCategory category, float dominant, float recessive)
        {
            Gene rareGene = new Gene(traitName, category, dominant, recessive, true);
            rareGene.isRare = true;
            rareGene.expressionStrength = Random.Range(0.5f, 1.0f);
            genes[traitName] = rareGene;
        }
        
        private void BreedFromParents(FishGenetics parent1, FishGenetics parent2)
        {
            // Get all unique trait names from both parents
            HashSet<string> allTraits = new HashSet<string>();
            allTraits.UnionWith(parent1.genes.Keys);
            allTraits.UnionWith(parent2.genes.Keys);
            
            foreach (string traitName in allTraits)
            {
                Gene parent1Gene = parent1.genes.ContainsKey(traitName) ? parent1.genes[traitName] : null;
                Gene parent2Gene = parent2.genes.ContainsKey(traitName) ? parent2.genes[traitName] : null;
                
                Gene childGene = InheritTrait(parent1Gene, parent2Gene, traitName);
                
                // Apply mutation
                if (Random.Range(0f, 1f) < mutationRate)
                {
                    ApplyMutation(childGene);
                }
                
                genes[traitName] = childGene;
            }
            
            // Chance for new rare traits to emerge
            if (Random.Range(0f, 1f) < 0.02f) // 2% chance for completely new trait
            {
                GenerateNewRareTrait();
            }
        }
        
        private Gene InheritTrait(Gene parent1Gene, Gene parent2Gene, string traitName)
        {
            // If only one parent has the trait, inherit from that parent
            if (parent1Gene == null && parent2Gene != null)
                return new Gene(parent2Gene.traitName, parent2Gene.category, parent2Gene.dominantValue, parent2Gene.recessiveValue, parent2Gene.isDominant);
            if (parent2Gene == null && parent1Gene != null)
                return new Gene(parent1Gene.traitName, parent1Gene.category, parent1Gene.dominantValue, parent1Gene.recessiveValue, parent1Gene.isDominant);
            
            // If neither parent has the trait, create a default
            if (parent1Gene == null && parent2Gene == null)
                return new Gene(traitName, TraitCategory.Physical, 1.0f, 1.0f, true);
            
            // Both parents have the trait - Mendelian inheritance
            Gene childGene = new Gene(traitName, parent1Gene.category, 0f, 0f, true);
            
            // Randomly inherit from each parent
            bool inheritFromParent1Dominant = Random.Range(0f, 1f) > 0.5f;
            bool inheritFromParent2Dominant = Random.Range(0f, 1f) > 0.5f;
            
            float value1 = inheritFromParent1Dominant ? parent1Gene.dominantValue : parent1Gene.recessiveValue;
            float value2 = inheritFromParent2Dominant ? parent2Gene.dominantValue : parent2Gene.recessiveValue;
            
            // Determine dominance
            bool gene1Dominant = parent1Gene.isDominant && inheritFromParent1Dominant;
            bool gene2Dominant = parent2Gene.isDominant && inheritFromParent2Dominant;
            
            if (gene1Dominant && !gene2Dominant)
            {
                childGene.dominantValue = value1;
                childGene.recessiveValue = value2;
                childGene.isDominant = true;
            }
            else if (!gene1Dominant && gene2Dominant)
            {
                childGene.dominantValue = value2;
                childGene.recessiveValue = value1;
                childGene.isDominant = true;
            }
            else
            {
                // Co-dominance or blending
                childGene.dominantValue = (value1 + value2) / 2f;
                childGene.recessiveValue = (value1 + value2) / 2f;
                childGene.isDominant = Random.Range(0f, 1f) > 0.5f;
            }
            
            // Inherit rare trait status
            childGene.isRare = parent1Gene.isRare || parent2Gene.isRare;
            
            return childGene;
        }
        
        private void ApplyMutation(Gene gene)
        {
            float mutationStrength = Random.Range(0.1f, 0.3f);
            
            // Mutate values
            if (Random.Range(0f, 1f) > 0.5f)
            {
                gene.dominantValue += Random.Range(-mutationStrength, mutationStrength);
            }
            else
            {
                gene.recessiveValue += Random.Range(-mutationStrength, mutationStrength);
            }
            
            // Rare chance for dominance to flip
            if (Random.Range(0f, 1f) < 0.1f)
            {
                gene.isDominant = !gene.isDominant;
            }
            
            // Very rare chance to become a rare trait
            if (Random.Range(0f, 1f) < 0.01f)
            {
                gene.isRare = true;
                gene.category = TraitCategory.Special;
            }
        }
        
        private void GenerateNewRareTrait()
        {
            string[] rareTraitNames = { "psychic", "timeManipulation", "phaseShift", "energyAbsorption", "dimensionalTravel" };
            string newTraitName = rareTraitNames[Random.Range(0, rareTraitNames.Length)];
            
            if (!genes.ContainsKey(newTraitName))
            {
                AddRareGene(newTraitName, TraitCategory.Special, Random.Range(0.5f, 1.0f), 0f);
            }
        }
        
        public float GetTraitValue(string traitName, float defaultValue = 1.0f)
        {
            if (genes.ContainsKey(traitName))
            {
                return genes[traitName].GetExpressedValue();
            }
            return defaultValue;
        }
        
        public Color GetColorTrait(string colorTraitBase, Color defaultColor)
        {
            float r = GetTraitValue(colorTraitBase + "R", defaultColor.r);
            float g = GetTraitValue(colorTraitBase + "G", defaultColor.g);
            float b = GetTraitValue(colorTraitBase + "B", defaultColor.b);
            
            return new Color(Mathf.Clamp01(r), Mathf.Clamp01(g), Mathf.Clamp01(b), defaultColor.a);
        }
        
        public bool HasRareTrait(string traitName)
        {
            return genes.ContainsKey(traitName) && genes[traitName].isRare;
        }
        
        public List<string> GetAllRareTraits()
        {
            return genes.Where(kvp => kvp.Value.isRare).Select(kvp => kvp.Key).ToList();
        }
        
        public float GetGeneticDiversity()
        {
            int rareTraitCount = GetAllRareTraits().Count;
            int totalTraitCount = genes.Count;
            
            return (float)rareTraitCount / totalTraitCount;
        }
        
        public string GetGeneticSummary()
        {
            var rareTraits = GetAllRareTraits();
            string summary = $"Genetic Diversity: {GetGeneticDiversity():P1}\n";
            
            if (rareTraits.Count > 0)
            {
                summary += $"Rare Traits: {string.Join(", ", rareTraits)}\n";
            }
            
            summary += $"Total Genes: {genes.Count}";
            return summary;
        }
    }
}
